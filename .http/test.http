
###
GET http://localhost:3000/api/content/faq-categories?projectId=2



###
POST http://localhost:3000/api/content/faq-categories
Content-Type: application/json

{
  "projectId": 2,
  "name": "FAQ Category 1",
  "slug": "faq-category-1",
  "description": "FAQ Category 1 Description"
}



###
GET http://localhost:3000/api/content/faq-categories/1?projectId=2

###
PUT http://localhost:3000/api/content/faq-categories/1?projectId=2
Content-Type: application/json

{
  "name": "FAQ Category 2",
  "slug": "faq-category-2",
  "description": "FAQ Category 1 Description"
}


###
GET http://localhost:3000/api/content/faq?projectId=2



###
POST http://localhost:3000/api/content/faq
Content-Type: application/json

{
  "projectId": 2,
  "question": "FAQ Question 1",
  "answer": "FAQ Answer 1",
  "categoryId": 1
}


###
GET http://localhost:3000/api/content/faq/1?projectId=2


###
PUT http://localhost:3000/api/content/faq/1?projectId=2
Content-Type: application/json

{
  "question": "FAQ Question 2",
  "answer": "FAQ Answer 2",
  "categoryId": 1
}