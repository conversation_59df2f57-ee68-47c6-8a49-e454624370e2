import { mainDb } from '../src/lib/db';
import { users, roles, userRoles, menus, systemConfig } from '../src/lib/db/schema/main';
import { hashPassword } from '../src/lib/crypto';

async function seed() {
  console.log('🌱 开始初始化数据库种子数据...');

  try {
    // 创建系统角色
    console.log('📝 创建系统角色...');
    const [superAdminRole] = await mainDb
      .insert(roles)
      .values({
        name: '超级管理员',
        description: '系统超级管理员，拥有所有权限',
        permissions: ['*'],
        isSystem: true,
      })
      .returning();

    const [adminRole] = await mainDb
      .insert(roles)
      .values({
        name: '管理员',
        description: '系统管理员，可管理业务项目和用户',
        permissions: [
          'projects:read',
          'projects:create',
          'projects:update',
          'projects:delete',
          'users:read',
          'users:create',
          'users:update',
          'content:read',
          'content:create',
          'content:update',
          'content:delete',
        ],
        isSystem: true,
      })
      .returning();
    console.log(adminRole);
    
    const [operatorRole] = await mainDb
      .insert(roles)
      .values({
        name: '操作员',
        description: '系统操作员，可查看和管理内容',
        permissions: [
          'projects:read',
          'users:read',
          'content:read',
          'content:create',
          'content:update',
        ],
        isSystem: true,
      })
      .returning();

    // 创建默认用户
    console.log('👤 创建默认用户...');
    const { hash: adminPasswordHash, salt: adminSalt } = await hashPassword('admin123');
    const [adminUser] = await mainDb
      .insert(users)
      .values({
        email: '<EMAIL>',
        name: '系统管理员',
        password: `${adminPasswordHash}:${adminSalt}`,
        isActive: true,
      })
      .returning();

    const { hash: operatorPasswordHash, salt: operatorSalt } = await hashPassword('operator123');
    const [operatorUser] = await mainDb
      .insert(users)
      .values({
        email: '<EMAIL>',
        name: '系统操作员',
        password: `${operatorPasswordHash}:${operatorSalt}`,
        isActive: true,
      })
      .returning();

    // 分配角色
    console.log('🔗 分配用户角色...');
    await mainDb.insert(userRoles).values([
      {
        userId: adminUser.id,
        roleId: superAdminRole.id,
      },
      {
        userId: operatorUser.id,
        roleId: operatorRole.id,
      },
    ]);

    // 创建系统菜单
    console.log('📋 创建系统菜单...');
    
    // 先创建主菜单
    const mainMenus = [
      {
        name: '概览',
        path: '/admin',
        icon: 'LayoutDashboard',
        sort: 1,
        permissions: ['admin:read'],
      },
      {
        name: '业务项目',
        path: '/admin/projects',
        icon: 'Database',
        sort: 2,
        permissions: ['projects:read'],
      },
      {
        name: '用户管理',
        path: '/admin/users',
        icon: 'Users',
        sort: 3,
        permissions: ['users:read'],
      },
      {
        name: '角色权限',
        path: '/admin/roles',
        icon: 'Shield',
        sort: 4,
        permissions: ['roles:read'],
      },
      {
        name: '菜单管理',
        path: '/admin/menus',
        icon: 'Menu',
        sort: 5,
        permissions: ['menus:read'],
      },
      {
        name: '内容管理',
        path: '/admin/content',
        icon: 'FileText',
        sort: 6,
        permissions: ['content:read'],
      },
      {
        name: '站点配置',
        path: '/admin/site-config',
        icon: 'Globe',
        sort: 7,
        permissions: ['config:read'],
      },
      {
        name: '系统设置',
        path: '/admin/settings',
        icon: 'Settings',
        sort: 8,
        permissions: ['system:read'],
      },
    ];

    const createdMainMenus = await mainDb.insert(menus).values(mainMenus).returning();

    // 找到内容管理菜单的ID
    const contentMenu = createdMainMenus.find(menu => menu.name === '内容管理');
    
    // 创建内容管理的子菜单
    if (contentMenu) {
      const subMenus = [
        {
          name: 'Meta配置',
          path: '/admin/content/meta',
          icon: 'Search',
          parentId: contentMenu.id,
          sort: 1,
          permissions: ['content:read'],
        },
        {
          name: '博客管理',
          path: '/admin/content/blog',
          icon: 'BookOpen',
          parentId: contentMenu.id,
          sort: 2,
          permissions: ['content:read'],
        },
        {
          name: '文件管理',
          path: '/admin/content/files',
          icon: 'FolderOpen',
          parentId: contentMenu.id,
          sort: 3,
          permissions: ['content:read'],
        },
      ];

      await mainDb.insert(menus).values(subMenus);
    }

    // 创建系统配置
    console.log('⚙️ 创建系统配置...');
    const systemConfigData = [
      {
        key: 'system_name',
        value: '多业务统一管理平台',
        description: '系统名称',
      },
      {
        key: 'system_version',
        value: '1.0.0',
        description: '系统版本',
      },
      {
        key: 'max_projects',
        value: 50,
        description: '最大项目数量限制',
      },
      {
        key: 'auto_backup',
        value: true,
        description: '是否启用自动备份',
      },
      {
        key: 'backup_interval',
        value: 24,
        description: '备份间隔（小时）',
      },
    ];

    await mainDb.insert(systemConfig).values(systemConfigData);

    console.log('✅ 数据库种子数据初始化完成！');
    console.log('\n📋 默认账号信息:');
    console.log('🔐 超级管理员:');
    console.log('   邮箱: <EMAIL>');
    console.log('   密码: admin123');
    console.log('\n🔐 操作员:');
    console.log('   邮箱: <EMAIL>');
    console.log('   密码: operator123');

  } catch (error) {
    console.error('❌ 种子数据初始化失败:', error);
    process.exit(1);
  }
}

// 运行种子脚本
seed()
  .then(() => {
    console.log('🎉 种子数据初始化完成，程序退出');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 种子脚本执行失败:', error);
    process.exit(1);
  }); 