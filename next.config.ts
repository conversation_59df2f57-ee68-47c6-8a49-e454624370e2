import type { NextConfig } from "next";

// 只在开发环境下输出日志
if (process.env.NODE_ENV !== "production") {
  console.log("---NODE_ENV", process.env.NODE_ENV);
}

const nextConfig: NextConfig = {
  output: "standalone",
  pageExtensions: ["js", "jsx", "md", "mdx", "ts", "tsx"],

  // 在生产环境中移除 console.log
  compiler: {
    removeConsole:
      process.env.NODE_ENV === "production"
        ? {
            exclude: ["error", "warn"], // 保留 console.error 和 console.warn
          }
        : false,
  },

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.pumpsoul.com',  // 使用 ** 匹配所有子域名
        port: '',
        pathname: '/**',              // 允许所有路径
        search: '',
      },
    ],
  },

  /* config options here */
  webpack: (config, { isServer }) => {
    // 只在客户端构建时排除这些 Node.js 模块
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        "pg-native": false,
      };
    }

    return config;
  },
  serverExternalPackages: ["pg", "postgres"],
};


export default nextConfig;
