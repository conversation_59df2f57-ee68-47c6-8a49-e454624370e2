CREATE TABLE "business_projects" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"domain" varchar(255),
	"db_host" varchar(255) NOT NULL,
	"db_port" integer DEFAULT 5432,
	"db_name" varchar(255) NOT NULL,
	"db_user" varchar(255) NOT NULL,
	"db_password" text NOT NULL,
	"is_active" boolean DEFAULT true,
	"config" jsonb,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	"created_by" uuid
);
--> statement-breakpoint
-- 为 business_projects 表添加注释
COMMENT ON TABLE "business_projects" IS '业务项目表';
COMMENT ON COLUMN "business_projects"."name" IS '项目名称';
COMMENT ON COLUMN "business_projects"."description" IS '项目描述';
COMMENT ON COLUMN "business_projects"."domain" IS '域名';
COMMENT ON COLUMN "business_projects"."db_host" IS '数据库主机';
COMMENT ON COLUMN "business_projects"."db_port" IS '数据库端口';
COMMENT ON COLUMN "business_projects"."db_name" IS '数据库名称';
COMMENT ON COLUMN "business_projects"."db_user" IS '数据库用户';
COMMENT ON COLUMN "business_projects"."db_password" IS '数据库密码';
COMMENT ON COLUMN "business_projects"."is_active" IS '是否激活';
COMMENT ON COLUMN "business_projects"."config" IS '配置';
COMMENT ON COLUMN "business_projects"."created_at" IS '创建时间';
COMMENT ON COLUMN "business_projects"."updated_at" IS '更新时间';
COMMENT ON COLUMN "business_projects"."created_by" IS '创建者';
--> statement-breakpoint
CREATE TABLE "menus" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(100) NOT NULL,
	"path" varchar(255),
	"icon" varchar(100),
	"parent_id" integer,
	"sort" integer DEFAULT 0,
	"is_visible" boolean DEFAULT true,
	"permissions" jsonb,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
-- 为 menus 表添加注释
COMMENT ON TABLE "menus" IS '菜单表';
COMMENT ON COLUMN "menus"."name" IS '菜单名称';
COMMENT ON COLUMN "menus"."path" IS '菜单路径';
COMMENT ON COLUMN "menus"."icon" IS '菜单图标';
COMMENT ON COLUMN "menus"."parent_id" IS '父级ID';
COMMENT ON COLUMN "menus"."sort" IS '排序';
COMMENT ON COLUMN "menus"."is_visible" IS '是否可见';
COMMENT ON COLUMN "menus"."permissions" IS '权限';
COMMENT ON COLUMN "menus"."created_at" IS '创建时间';
COMMENT ON COLUMN "menus"."updated_at" IS '更新时间';
--> statement-breakpoint
CREATE TABLE "roles" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"permissions" jsonb,
	"is_system" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "roles_name_unique" UNIQUE("name")
);
--> statement-breakpoint
-- 为 roles 表添加注释
COMMENT ON TABLE "roles" IS '角色表';
COMMENT ON COLUMN "roles"."name" IS '角色名称';
COMMENT ON COLUMN "roles"."description" IS '角色描述';
COMMENT ON COLUMN "roles"."permissions" IS '权限';
COMMENT ON COLUMN "roles"."is_system" IS '是否系统内置';
COMMENT ON COLUMN "roles"."created_at" IS '创建时间';
COMMENT ON COLUMN "roles"."updated_at" IS '更新时间';
--> statement-breakpoint
CREATE TABLE "system_config" (
	"id" serial PRIMARY KEY NOT NULL,
	"key" varchar(255) NOT NULL,
	"value" jsonb,
	"description" text,
	"updated_at" timestamp DEFAULT now(),
	"updated_by" uuid,
	CONSTRAINT "system_config_key_unique" UNIQUE("key")
);
--> statement-breakpoint
-- 为 system_config 表添加注释
COMMENT ON TABLE "system_config" IS '系统配置表';
COMMENT ON COLUMN "system_config"."key" IS '配置键';
COMMENT ON COLUMN "system_config"."value" IS '配置值';
COMMENT ON COLUMN "system_config"."description" IS '配置描述';
COMMENT ON COLUMN "system_config"."updated_at" IS '更新时间';
COMMENT ON COLUMN "system_config"."updated_by" IS '更新者';
--> statement-breakpoint
CREATE TABLE "user_roles" (
	"id" serial PRIMARY KEY NOT NULL,
	"user_id" uuid,
	"role_id" integer,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
-- 为 user_roles 表添加注释
COMMENT ON TABLE "user_roles" IS '用户角色关联表';
COMMENT ON COLUMN "user_roles"."user_id" IS '用户ID';
COMMENT ON COLUMN "user_roles"."role_id" IS '角色ID';
COMMENT ON COLUMN "user_roles"."created_at" IS '创建时间';
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(255) NOT NULL,
	"name" varchar(255),
	"avatar" text,
	"password" varchar(255),
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
-- 为 users 表添加注释
COMMENT ON TABLE "users" IS '用户表';
COMMENT ON COLUMN "users"."email" IS '邮箱';
COMMENT ON COLUMN "users"."name" IS '用户名';
COMMENT ON COLUMN "users"."avatar" IS '头像';
COMMENT ON COLUMN "users"."password" IS '密码';
COMMENT ON COLUMN "users"."is_active" IS '是否激活';
COMMENT ON COLUMN "users"."created_at" IS '创建时间';
COMMENT ON COLUMN "users"."updated_at" IS '更新时间';
--> statement-breakpoint
ALTER TABLE "business_projects" ADD CONSTRAINT "business_projects_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
--> statement-breakpoint
ALTER TABLE "system_config" ADD CONSTRAINT "system_config_updated_by_users_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_roles_id_fk" FOREIGN KEY ("role_id") REFERENCES "public"."roles"("id") ON DELETE cascade ON UPDATE no action;