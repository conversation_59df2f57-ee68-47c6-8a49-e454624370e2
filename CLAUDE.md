# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Development
pnpm dev              # Start development server with turbopack
pnpm build            # Build for production
pnpm start            # Start production server
pnpm lint             # Run ESLint

# Database Management
pnpm db:generate      # Generate Drizzle migrations
pnpm db:push          # Push schema changes to database
pnpm db:migrate       # Run migrations
pnpm db:studio        # Open Drizzle Studio
pnpm db:seed          # Seed database with initial data
```

## Architecture Overview

### Multi-tenant Admin Panel System
This is a unified admin system for managing multiple overseas projects, built with Next.js 15, TypeScript, Tailwind CSS, Shadcn UI, Drizzle ORM, and Supabase.

### Database Architecture
- **Main Database**: System-level management (users, roles, permissions, menus, business project configurations)
- **Business Databases**: Dynamically connected per-project databases containing business-specific data

### Key Components

#### Database Management (`src/lib/db-manager.ts`)
- Connection pooling for multiple business databases
- Dynamic database instance creation based on project configuration
- Encrypted database credentials storage
- Connection lifecycle management

#### Authentication (`src/lib/auth.ts`)
- NextAuth.js with credential provider
- Default admin account (email: any, password: from `DEFAULT_ADMIN_PASSWORD` env var or 'admin123')
- JWT session strategy with 7-day expiration
- Password encryption using crypto utilities

#### Database Schemas
- **Main Schema** (`src/lib/db/schema/main.ts`): users, roles, menus, business_projects, system_config
- **Business Schema** (`src/lib/db/schema/business.ts`): page_meta, blog_categories, blog_posts, blog_tags, faq, site_config, page_stats

#### Project Structure
- `/app/admin/**` - System-level admin modules
- `/app/api/content/**` - Business data APIs
- `/app/api/projects/**` - Project management APIs
- `/components/ui/**` - Shadcn UI components
- `/lib/db/**` - Database schemas and utilities

### Multi-project Support
- Each business project has its own database connection
- Dynamic database switching via `getDatabaseConnection(projectId)`
- Encrypted storage of database credentials
- Connection pooling for performance

### Technology Stack
- Next.js 15 with App Router
- TypeScript
- Tailwind CSS + Shadcn UI
- Drizzle ORM with PostgreSQL
- NextAuth.js for authentication
- TipTap for rich text editing
- Zustand for state management

### Development Notes
- Use `pnpm` as package manager
- Environment variables in `.env.local`
- Main database connection via `MAIN_DATABASE_URL`
- Default admin password via `DEFAULT_ADMIN_PASSWORD`
- Database migrations managed through Drizzle Kit
- Connection pooling automatically managed in production