{"$schema": "https://docs.cursor.com/context/rules.schema.json", "name": "Multitenant Admin Panel", "description": "统一管理多个出海项目的后台系统，基于 Next.js + TypeScript + Tailwind + Shadcn UI + Drizzle ORM + Supabase 构建，支持动态新增业务数据库。", "tags": ["nextjs", "typescript", "tailwind", "drizzle-orm", "supabase", "shadcn", "multitenancy", "admin"], "packageManager": "pnpm", "rules": [{"name": "主库结构", "description": "主库用于统一管理系统级信息，如用户、角色、权限、菜单、业务数据库配置等。", "examples": ["users、roles、permissions、menus 表位于主数据库中。", "tenants 表用于记录各业务数据库的连接信息，包括名称、host、port、schema 等字段。"]}, {"name": "动态业务库支持", "description": "支持动态接入新的业务数据库，业务数据库包含统一的核心表结构（如 blog、tdk），也可以扩展为自定义业务功能。", "examples": ["每个业务项目对应一个数据库，新增时仅需配置连接信息。", "动态加载业务数据库 schema，并按请求自动切换 drizzle 实例。"]}, {"name": "多项目多租户结构", "description": "系统支持多个 web 项目管理，每个项目租户可独立运行，后续可支持子域或 path-based 路由。", "examples": ["支持多租户切换，用户登录后基于租户权限加载资源。", "可基于 subdomain（如 projectA.example.com）或 path（如 /project-a）识别当前项目。"]}, {"name": "UI 技术规范", "description": "统一使用 Tailwind CSS + Shadcn UI 实现后台界面，配合 Lucide 图标、React Hook Form 与 Zod 实现表单逻辑。", "examples": ["使用 shadcn UI 的 Dialog、Select、DataTable 等组件构建管理台 UI。", "表单使用 react-hook-form + zod，表格使用 tanstack/react-table。"]}, {"name": "ORM 和数据库结构", "description": "使用 Drizzle ORM 管理 schema，drizzle-kit 提供迁移工具，主库和业务库分别维护。", "examples": ["主库 schema 位于 lib/db/schema/main 下，业务库 schema 位于 lib/db/schema/business。", "使用 drizzle-kit migrate/push/generate 管理数据库变更。"]}, {"name": "认证和权限系统", "description": "采用 NextAuth.js + @auth/drizzle-adapter 实现登录认证，角色权限映射保存在主库中。", "examples": ["登录后基于 Session 加载用户绑定的租户及权限信息。", "支持基于角色控制访问菜单与页面。"]}, {"name": "目录结构建议", "description": "推荐按照模块组织项目目录，主库与业务库逻辑分开。", "examples": ["/app/admin/** 存放系统级模块，如用户、角色、租户管理。", "/app/[tenant]/** 存放具体业务模块。", "/lib/db/clients.ts 动态管理多数据库连接。"]}, {"name": "开发流程建议", "description": "使用 pnpm + turbopack 提升开发效率，使用 tsx 运行脚本、统一 lint 规范。", "examples": ["使用 scripts/seed.ts 进行本地初始化数据。", "新增业务库后，运行 db:generate 更新类型定义。"]}]}