"use client";

import React, { useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ImageUploadProps {
  value?: string;
  onChange: (url: string) => void;
  projectId?: number ;
  label?: string;
  className?: string;
}

/**
 * 通用图片上传组件，支持projectId，上传后支持预览
 */
export function ImageUpload({ value, onChange, projectId, label, className }: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [preview, setPreview] = useState<string | undefined>(value);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [alertInfo, setAlertInfo] = useState<{ message: string; type?: 'default' | 'destructive' } | null>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setUploading(true);
    const formData = new FormData();
    formData.append("file", file);
    try {
      const res = await fetch(`/api/upload?projectId=${projectId}`, {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      if (data.success && data.url) {
        setPreview(data.url);
        onChange(data.url);
      } else {
        setAlertInfo({ message: data.error || '上传失败', type: 'destructive' });
      }
    } catch {
      setAlertInfo({ message: '上传失败', type: 'destructive' });
    } finally {
      setUploading(false);
    }
  };

  return (
    <>
      {alertInfo && (
        <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-full max-w-md">
          <Alert variant={alertInfo.type || 'default'}>
            <AlertDescription>{alertInfo.message}</AlertDescription>
          </Alert>
        </div>
      )}
      <div className={className}>
        {label && <div className="mb-1 font-medium">{label}</div>}
        <div className="flex items-center gap-4">
          <Input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            disabled={uploading}
            className="w-auto"
          />
          {preview ? (
            <img
              src={preview}
              alt="logo预览"
              className="w-16 h-16 object-contain border rounded bg-white"
            />
          ) : uploading ? (
            <Skeleton className="w-16 h-16" />
          ) : null}
        </div>
      </div>
    </>
  );
}

export default ImageUpload; 