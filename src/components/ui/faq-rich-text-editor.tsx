"use client";

import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import { Link } from '@tiptap/extension-link';
import { Placeholder } from '@tiptap/extension-placeholder';
import { Underline } from '@tiptap/extension-underline';
import { Button } from '@/components/ui/button';
import { Bold, Italic, Underline as UnderlineIcon, Strikethrough, Link as LinkIcon, Unlink } from 'lucide-react';

interface FaqRichTextEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function FaqRichTextEditor({ 
  content = '', 
  onChange,
  placeholder = '请输入问题的详细答案...',
  className = '',
  disabled = false
}: FaqRichTextEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        // Disable heading functionality
        heading: false,
        // Keep other formatting options
        bold: true,
        italic: false, // We'll use underline instead of italic
        strike: true,
        code: false,
        codeBlock: false,
        blockquote: false,
        horizontalRule: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline cursor-pointer',
        },
      }),
      Underline,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: content ? JSON.parse(content) : '',
    immediatelyRender: false,
    editable: !disabled,
    onUpdate: ({ editor }) => {
      onChange?.(JSON.stringify(editor.getJSON()));
    },
  });

  const addLink = () => {
    const url = window.prompt('请输入链接地址:');
    if (url) {
      editor?.chain().focus().setLink({ href: url }).run();
    }
  };

  const removeLink = () => {
    editor?.chain().focus().unsetLink().run();
  };

  if (!editor) {
    return null;
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* 工具栏 */}
      <div className="border-b p-2 flex flex-wrap gap-1">
        {/* 粗体 */}
        <Button
          type="button"
          variant={editor.isActive('bold') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          disabled={disabled}
          title="粗体"
        >
          <Bold className="h-4 w-4" />
        </Button>
        
        {/* 下划线 */}
        <Button
          type="button"
          variant={editor.isActive('underline') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleUnderline().run()}
          disabled={disabled}
          title="下划线"
        >
          <UnderlineIcon className="h-4 w-4" />
        </Button>
        
        {/* 删除线 */}
        <Button
          type="button"
          variant={editor.isActive('strike') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
          disabled={disabled}
          title="删除线"
        >
          <Strikethrough className="h-4 w-4" />
        </Button>

        {/* 分隔符 */}
        <div className="w-px h-6 bg-border mx-1" />
        
        {/* 链接 */}
        <Button
          type="button"
          variant={editor.isActive('link') ? 'default' : 'outline'}
          size="sm"
          onClick={addLink}
          disabled={disabled}
          title="添加链接"
        >
          <LinkIcon className="h-4 w-4" />
        </Button>
        
        {/* 移除链接 */}
        {editor.isActive('link') && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={removeLink}
            disabled={disabled}
            title="移除链接"
          >
            <Unlink className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* 编辑器内容区域 */}
      <div className="p-3 min-h-[200px]">
        <EditorContent 
          editor={editor} 
          className="prose prose-sm max-w-none focus:outline-none"
        />
      </div>
    </div>
  );
}
