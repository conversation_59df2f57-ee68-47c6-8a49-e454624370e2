"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Save,
  Eye,
  Plus,
  X,
  Settings,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { Project } from '@/types/project';
import { BlogCategory, BlogPost } from '@/types/blog';
import dynamic from 'next/dynamic';
// 动态导入富文本编辑器，确保只在客户端渲染
const RichTextEditor = dynamic(
  () => import('@/components/ui/rich-text-editor').then((mod) => ({ default: mod.RichTextEditor })),
  {
    ssr: false, // 禁用服务器端渲染
    loading: () => (
      <div className="border rounded-lg p-4 min-h-[300px] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">编辑器加载中...</p>
        </div>
      </div>
    ),
  }
);

interface BlogPostFormProps {
  project: Project;
  initialData?: Partial<BlogPost>;
  mode: 'create' | 'edit';
  onSubmit: (data: BlogPost, status: string) => Promise<void>;
  saving: boolean;
  error: string | null;
}

export function BlogPostForm({ 
  project, 
  initialData, 
  mode, 
  onSubmit, 
  saving, 
  error 
}: BlogPostFormProps) {
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState<BlogPost>({
    title: initialData?.title || '',
    slug: initialData?.slug || '',
    summary: initialData?.summary || '',
    content: initialData?.content || '',
    categoryId: initialData?.categoryId || '',
    tags: initialData?.tags || [],
    status: initialData?.status || 'draft',
    metaTitle: initialData?.metaTitle || '',
    metaDescription: initialData?.metaDescription || '',
    metaKeywords: initialData?.metaKeywords || '',
    authorName: initialData?.authorName || '',
    authorEmail: initialData?.authorEmail || '',
    ...initialData
  });
  
  const [newTag, setNewTag] = useState('');
  const [showSEO, setShowSEO] = useState(false);

  const loadCategories = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/content/categories?projectId=${project.id}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data || []);
        }
      }
    } catch (err) {
      console.error('Load categories error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (project) {
      loadCategories();
    }
  }, [project]);

  // 从标题自动生成slug
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 如果是标题字段且slug为空，自动生成slug (仅在创建模式下)
    if (field === 'title' && mode === 'create' && !formData.slug) {
      setFormData(prev => ({
        ...prev,
        slug: generateSlug(value)
      }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async (status: string) => {
    if (!formData.title.trim() || !formData.content.trim()) {
      return;
    }

    await onSubmit(formData, status);
  };

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="flex items-center gap-2 py-4">
            <AlertCircle className="h-5 w-5 text-destructive" />
            <span className="text-destructive">{error}</span>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
              <CardDescription>
                填写文章的基本信息和内容
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="title">文章标题 *</Label>
                  <Input
                    id="title"
                    placeholder="输入文章标题"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slug">URL别名</Label>
                  <Input
                    id="slug"
                    placeholder="url-friendly-slug"
                    value={formData.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="summary">文章摘要</Label>
                <Textarea
                  id="summary"
                  placeholder="简要描述文章内容..."
                  value={formData.summary}
                  onChange={(e) => handleInputChange('summary', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>文章内容 *</Label>
                <RichTextEditor
                  content={formData.content}
                  onChange={(content) => handleInputChange('content', content)}
                  placeholder="开始写作..."
                />
              </div>
            </CardContent>
          </Card>

          {/* SEO 设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                SEO 设置
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSEO(!showSEO)}
                >
                  {showSEO ? '收起' : '展开'}
                </Button>
              </CardTitle>
              <CardDescription>
                优化搜索引擎表现
              </CardDescription>
            </CardHeader>
            {showSEO && (
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="metaTitle">SEO标题</Label>
                  <Input
                    id="metaTitle"
                    placeholder="SEO友好的标题"
                    value={formData.metaTitle}
                    onChange={(e) => handleInputChange('metaTitle', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metaDescription">SEO描述</Label>
                  <Textarea
                    id="metaDescription"
                    placeholder="用于搜索引擎的描述..."
                    value={formData.metaDescription}
                    onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metaKeywords">关键词</Label>
                  <Input
                    id="metaKeywords"
                    placeholder="关键词，用逗号分隔"
                    value={formData.metaKeywords}
                    onChange={(e) => handleInputChange('metaKeywords', e.target.value)}
                  />
                </div>
              </CardContent>
            )}
          </Card>
        </div>

        <div className="space-y-6">
          {/* 操作按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                onClick={() => handleSubmit('draft')}
                disabled={saving || !formData.title.trim() || !formData.content.trim()}
                className="w-full"
              >
                {saving ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                保存草稿
              </Button>
              <Button 
                onClick={() => handleSubmit('published')}
                disabled={saving || !formData.title.trim() || !formData.content.trim()}
                className="w-full"
              >
                {saving ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Eye className="mr-2 h-4 w-4" />
                )}
                发布文章
              </Button>
            </CardContent>
          </Card>

          {/* 发布设置 */}
          <Card>
            <CardHeader>
              <CardTitle>发布设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="authorName">作者姓名</Label>
                <Input
                  id="authorName"
                  placeholder="作者姓名"
                  value={formData.authorName}
                  onChange={(e) => handleInputChange('authorName', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="authorEmail">作者邮箱</Label>
                <Input
                  id="authorEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.authorEmail}
                  onChange={(e) => handleInputChange('authorEmail', e.target.value)}
                />
              </div>
            </CardContent>
          </Card>

          {/* 分类和标签 */}
          <Card>
            <CardHeader>
              <CardTitle>分类和标签</CardTitle>
              <CardDescription>
                为文章设置分类和标签
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="category">文章分类</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="h-8"
                  >
                    <a 
                      href={`/admin/content/categories?projectId=${project.id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Settings className="mr-1 h-3 w-3" />
                      管理分类
                    </a>
                  </Button>
                </div>
                {loading ? (
                  <div className="flex items-center gap-2 p-2 border rounded">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">加载分类中...</span>
                  </div>
                ) : (
                  <Select value={formData.categoryId} onValueChange={(value) => handleInputChange('categoryId', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div className="space-y-2">
                <Label>文章标签</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="添加标签..."
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                  />
                  <Button type="button" onClick={handleAddTag} disabled={!newTag.trim()}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 