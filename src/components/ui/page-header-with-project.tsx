"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import { ProjectSelector } from '@/components/ui/project-selector';
import { Project } from '@/types/project';

interface PageHeaderWithProjectProps {
  title: string;
  description: string;
  selectedProject: Project | null;
  onProjectChange: (projectId: number, project: Project) => void;
  actions?: React.ReactNode;
  error?: string | null;
  onRetry?: () => void;
  className?: string;
  prefixElement?: React.ReactNode; // 前置元素，比如返回按钮
}

export function PageHeaderWithProject({
  title,
  description,
  selectedProject,
  onProjectChange,
  actions,
  error,
  onRetry,
  className = "",
  prefixElement,
}: PageHeaderWithProjectProps) {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* 页面标题、项目选择器和操作 */}
      <div className="flex items-center justify-between gap-6">
        {/* 左侧：前置元素、标题和描述 */}
        <div className="flex-shrink-0">
          {prefixElement && (
            <div className="flex items-center gap-4 mb-2">
              {prefixElement}
            </div>
          )}
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          <p className="text-muted-foreground">{description}</p>
        </div>

        {/* 中间：项目选择器 */}
        <div className="flex-1 max-w-md">
          <ProjectSelector
            selectedProjectId={selectedProject?.id}
            onProjectChange={onProjectChange}
          />
        </div>

        {/* 右侧：操作按钮 */}
        {actions && (
          <div className="flex-shrink-0">
            {actions}
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="flex items-center gap-2 py-4">
            <AlertCircle className="h-5 w-5 text-destructive" />
            <span className="text-destructive">{error}</span>
            {onRetry && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onRetry}
                className="ml-auto"
              >
                重试
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
} 