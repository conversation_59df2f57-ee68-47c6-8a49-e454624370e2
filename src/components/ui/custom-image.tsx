
'use client';
import Image from 'next/image';

export function OptImage({
  src,
  alt,
  width,
  height,
  quality,
  className,
}: {
  src: string;
  alt: string;
  width: number;
  height?: number;
  quality?: number;
  className?: string;
}) {
  return (
    <S3Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      quality={quality}
      className={className}
    />
  );
}

function S3Image({
  src,
  alt,
  width,
  height,
  quality,
  className,
}: {
  src: string;
  alt: string;
  width: number;
  height?: number;
  quality?: number;
  className?: string;
}) {
  if (src.includes('pumpsoul.com')) {
    return (
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height} // 实际在 loader 里不处理，因此随便填写
        quality={quality}
        loader={s3ImageLoader}
        className={className}
      />
    );
  } else {
    return (
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        quality={quality}
        className={className}
      />
    );
  }
}
export function s3ImageLoader({
  src,
  width,
  quality,
}: {
  src: string;
  width?: number;
  quality?: number;
  }) {
  return `${src}__op__resize,m_lfit,w_${width},limit_1,q_${quality || 80}__op__format,f_webp`;
}
