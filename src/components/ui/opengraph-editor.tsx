"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Share2,
  Eye,
  EyeOff,
  Check,
  Twitter,
  Facebook,
  Globe,
  Image as ImageIcon,
  Video,
  Music,
  BookOpen,
  ShoppingCart,
  Calendar,
  MapPin
} from 'lucide-react';
import { OpenGraphData } from '@/types/page-meta';

interface OpenGraphEditorProps {
  value: OpenGraphData | null;
  onChange: (value: OpenGraphData | null) => void;
  disabled?: boolean;
}

// OpenGraph类型配置
const OG_TYPES = {
  website: { label: '网站', icon: Globe },
  article: { label: '文章', icon: BookOpen },
  video: { label: '视频', icon: Video },
  'video.movie': { label: '电影', icon: Video },
  'video.episode': { label: '电视剧集', icon: Video },
  'video.tv_show': { label: '电视节目', icon: Video },
  'video.other': { label: '其他视频', icon: Video },
  music: { label: '音乐', icon: Music },
  'music.song': { label: '歌曲', icon: Music },
  'music.album': { label: '专辑', icon: Music },
  'music.playlist': { label: '播放列表', icon: Music },
  'music.radio_station': { label: '电台', icon: Music },
  product: { label: '产品', icon: ShoppingCart },
  book: { label: '书籍', icon: BookOpen },
  profile: { label: '个人资料', icon: Globe },
  event: { label: '活动', icon: Calendar },
  place: { label: '地点', icon: MapPin },
};

// Twitter卡片类型
const TWITTER_CARD_TYPES = {
  summary: { label: '摘要卡片', description: '默认卡片，包含标题、描述和缩略图' },
  summary_large_image: { label: '大图摘要卡片', description: '突出显示图片的卡片' },
  app: { label: '应用卡片', description: '推广移动应用的卡片' },
  player: { label: '播放器卡片', description: '包含视频/音频播放器的卡片' },
};

export default function OpenGraphEditor({ value, onChange, disabled = false }: OpenGraphEditorProps) {
  const [ogData, setOgData] = useState<OpenGraphData | null>(value);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('website');

  useEffect(() => {
    setOgData(value);
    if (value && value['og:type']) {
      setSelectedType(value['og:type']);
    }
  }, [value]);

  const handleFieldChange = (field: string, fieldValue: string | string[]) => {
    const newData = ogData ? { ...ogData } : {};
    
    if (fieldValue === '' || (Array.isArray(fieldValue) && fieldValue.length === 0)) {
      delete newData[field];
    } else {
      newData[field] = fieldValue;
    }
    
    setOgData(newData);
    onChange(Object.keys(newData).length > 0 ? newData : null);
  };

  const handleTypeChange = (type: string) => {
    setSelectedType(type);
    handleFieldChange('og:type', type);
  };

  const clearOpenGraph = () => {
    setOgData(null);
    onChange(null);
  };

  const renderFieldInput = (
    label: string,
    field: string,
    value: string | undefined,
    type: string = 'text',
    placeholder?: string
  ) => {
    return (
      <div className="space-y-2">
        <Label htmlFor={field}>{label}</Label>
        {type === 'textarea' ? (
          <Textarea
            id={field}
            value={value || ''}
            onChange={(e) => handleFieldChange(field, e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
            rows={3}
          />
        ) : (
          <Input
            id={field}
            type={type}
            value={value || ''}
            onChange={(e) => handleFieldChange(field, e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
          />
        )}
      </div>
    );
  };

  const renderTagArrayInput = (label: string, field: string, value: string[] | undefined) => {
    return (
      <div className="space-y-2">
        <Label htmlFor={field}>{label}</Label>
        <Input
          id={field}
          value={value ? value.join(', ') : ''}
          onChange={(e) => handleFieldChange(field, e.target.value.split(',').map(tag => tag.trim()).filter(Boolean))}
          placeholder="用逗号分隔多个标签"
          disabled={disabled}
        />
      </div>
    );
  };

  const renderTypeSpecificFields = () => {
    if (!ogData) return null;

    const type = selectedType;

    switch (type) {
      case 'article':
        return (
          <div className="space-y-4">
            <h4 className="font-semibold text-sm">文章相关设置</h4>
            {renderFieldInput('作者', 'article:author', ogData['article:author'])}
            {renderFieldInput('发布时间', 'article:published_time', ogData['article:published_time'], 'datetime-local')}
            {renderFieldInput('修改时间', 'article:modified_time', ogData['article:modified_time'], 'datetime-local')}
            {renderFieldInput('文章分类', 'article:section', ogData['article:section'])}
            {renderTagArrayInput('文章标签', 'article:tag', ogData['article:tag'])}
          </div>
        );

      case 'video':
      case 'video.movie':
      case 'video.episode':
      case 'video.tv_show':
      case 'video.other':
        return (
          <div className="space-y-4">
            <h4 className="font-semibold text-sm">视频相关设置</h4>
            {renderFieldInput('视频URL', 'og:video', ogData['og:video'])}
            {renderFieldInput('视频宽度', 'og:video:width', ogData['og:video:width'], 'number')}
            {renderFieldInput('视频高度', 'og:video:height', ogData['og:video:height'], 'number')}
            {renderFieldInput('视频类型', 'og:video:type', ogData['og:video:type'], 'text', 'video/mp4')}
          </div>
        );

      case 'music':
      case 'music.song':
      case 'music.album':
      case 'music.playlist':
      case 'music.radio_station':
        return (
          <div className="space-y-4">
            <h4 className="font-semibold text-sm">音频相关设置</h4>
            {renderFieldInput('音频URL', 'og:audio', ogData['og:audio'])}
            {renderFieldInput('音频类型', 'og:audio:type', ogData['og:audio:type'], 'text', 'audio/mpeg')}
          </div>
        );

      case 'product':
        return (
          <div className="space-y-4">
            <h4 className="font-semibold text-sm">产品相关设置</h4>
            {renderFieldInput('价格', 'product:price:amount', ogData['product:price:amount'])}
            <div>
              <Label htmlFor="product:price:currency">货币</Label>
              <Select 
                value={ogData['product:price:currency'] || 'CNY'} 
                onValueChange={(value) => handleFieldChange('product:price:currency', value)}
                disabled={disabled}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CNY">人民币 (CNY)</SelectItem>
                  <SelectItem value="USD">美元 (USD)</SelectItem>
                  <SelectItem value="EUR">欧元 (EUR)</SelectItem>
                  <SelectItem value="GBP">英镑 (GBP)</SelectItem>
                  <SelectItem value="JPY">日元 (JPY)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="product:availability">可用性</Label>
              <Select 
                value={ogData['product:availability'] || 'in stock'} 
                onValueChange={(value) => handleFieldChange('product:availability', value)}
                disabled={disabled}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="in stock">有库存</SelectItem>
                  <SelectItem value="out of stock">无库存</SelectItem>
                  <SelectItem value="preorder">预订</SelectItem>
                  <SelectItem value="discontinued">停产</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const renderPreview = () => {
    if (!ogData) return null;

    return (
      <div className="space-y-4">
        {/* Facebook/Open Graph 预览 */}
        <div>
          <h4 className="font-semibold text-sm mb-2 flex items-center gap-2">
            <Facebook className="h-4 w-4" />
            Facebook 预览
          </h4>
          <Card className="p-4">
            <div className="border rounded-lg overflow-hidden">
              {ogData['og:image'] && (
                <div className="aspect-video bg-gray-100 flex items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div className="p-3 bg-gray-50">
                <div className="text-xs text-gray-500 uppercase mb-1">
                  {ogData['og:site_name'] || 'example.com'}
                </div>
                <div className="font-semibold text-sm text-gray-900 mb-1">
                  {ogData['og:title'] || '页面标题'}
                </div>
                <div className="text-xs text-gray-600 line-clamp-2">
                  {ogData['og:description'] || '页面描述'}
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Twitter 预览 */}
        <div>
          <h4 className="font-semibold text-sm mb-2 flex items-center gap-2">
            <Twitter className="h-4 w-4" />
            Twitter 预览
          </h4>
          <Card className="p-4">
            <div className="border rounded-lg overflow-hidden">
              {ogData['twitter:image'] && (
                <div className={`bg-gray-100 flex items-center justify-center ${
                  ogData['twitter:card'] === 'summary_large_image' ? 'aspect-video' : 'aspect-square'
                }`}>
                  <ImageIcon className="h-8 w-8 text-gray-400" />
                </div>
              )}
              <div className="p-3">
                <div className="font-semibold text-sm text-gray-900 mb-1">
                  {ogData['twitter:title'] || ogData['og:title'] || '页面标题'}
                </div>
                <div className="text-xs text-gray-600 line-clamp-2 mb-2">
                  {ogData['twitter:description'] || ogData['og:description'] || '页面描述'}
                </div>
                <div className="text-xs text-gray-500">
                  {ogData['twitter:site'] || '@example'}
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Share2 className="h-5 w-5" />
          Open Graph 社交分享
        </CardTitle>
        <CardDescription>
          配置页面在社交平台分享时的展示效果，包括Facebook、Twitter等平台
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 控制按钮 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              type="button"
              variant={isPreviewMode ? "outline" : "default"}
              size="sm"
              onClick={() => setIsPreviewMode(false)}
              disabled={disabled}
            >
              <Eye className="h-4 w-4 mr-2" />
              配置
            </Button>
            <Button
              type="button"
              variant={isPreviewMode ? "default" : "outline"}
              size="sm"
              onClick={() => setIsPreviewMode(true)}
              disabled={disabled || !ogData}
            >
              <EyeOff className="h-4 w-4 mr-2" />
              预览
            </Button>
          </div>

          <div className="flex items-center gap-2">
            {ogData && Object.keys(ogData).length > 0 && (
              <Badge variant="outline" className="text-green-600">
                <Check className="h-3 w-3 mr-1" />
                已配置
              </Badge>
            )}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={clearOpenGraph}
              disabled={disabled}
            >
              清空
            </Button>
          </div>
        </div>

        {/* 内容区域 */}
        {isPreviewMode ? (
          renderPreview()
        ) : (
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">基础设置</TabsTrigger>
              <TabsTrigger value="twitter">推特卡片</TabsTrigger>
              <TabsTrigger value="facebook">Facebook</TabsTrigger>
              <TabsTrigger value="advanced">高级设置</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="og-type">内容类型</Label>
                  <Select value={selectedType} onValueChange={handleTypeChange} disabled={disabled}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(OG_TYPES).map(([key, { label, icon: Icon }]) => (
                        <SelectItem key={key} value={key}>
                          <div className="flex items-center gap-2">
                            <Icon className="h-4 w-4" />
                            {label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="og-locale">语言</Label>
                  <Select 
                    value={ogData?.['og:locale'] || 'zh_CN'} 
                    onValueChange={(value) => handleFieldChange('og:locale', value)}
                    disabled={disabled}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="zh_CN">中文 (简体)</SelectItem>
                      <SelectItem value="zh_TW">中文 (繁体)</SelectItem>
                      <SelectItem value="en_US">英文</SelectItem>
                      <SelectItem value="ja_JP">日文</SelectItem>
                      <SelectItem value="ko_KR">韩文</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {renderFieldInput('页面标题', 'og:title', ogData?.['og:title'], 'text', '页面标题，建议60字符以内')}
              {renderFieldInput('页面描述', 'og:description', ogData?.['og:description'], 'textarea', '页面描述，建议160字符以内')}
              {renderFieldInput('页面URL', 'og:url', ogData?.['og:url'], 'url', 'https://example.com/page')}
              {renderFieldInput('网站名称', 'og:site_name', ogData?.['og:site_name'], 'text', '网站名称')}
              {renderFieldInput('分享图片', 'og:image', ogData?.['og:image'], 'url', 'https://example.com/image.jpg')}

              {renderTypeSpecificFields()}
            </TabsContent>

            <TabsContent value="twitter" className="space-y-4">
              <div>
                <Label htmlFor="twitter-card">卡片类型</Label>
                <Select 
                  value={ogData?.['twitter:card'] || 'summary'} 
                  onValueChange={(value) => handleFieldChange('twitter:card', value)}
                  disabled={disabled}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(TWITTER_CARD_TYPES).map(([key, { label, description }]) => (
                      <SelectItem key={key} value={key}>
                        <div>
                          <div className="font-medium">{label}</div>
                          <div className="text-xs text-gray-500">{description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {renderFieldInput('推特标题', 'twitter:title', ogData?.['twitter:title'], 'text', '推特卡片标题')}
              {renderFieldInput('推特描述', 'twitter:description', ogData?.['twitter:description'], 'textarea', '推特卡片描述')}
              {renderFieldInput('推特图片', 'twitter:image', ogData?.['twitter:image'], 'url', 'https://example.com/image.jpg')}
              {renderFieldInput('网站推特账号', 'twitter:site', ogData?.['twitter:site'], 'text', '@example')}
              {renderFieldInput('内容创作者', 'twitter:creator', ogData?.['twitter:creator'], 'text', '@creator')}

              {ogData?.['twitter:card'] === 'player' && (
                <div className="space-y-4">
                  <h4 className="font-semibold text-sm">播放器设置</h4>
                  {renderFieldInput('播放器URL', 'twitter:player', ogData?.['twitter:player'], 'url')}
                  {renderFieldInput('播放器宽度', 'twitter:player:width', ogData?.['twitter:player:width'], 'number')}
                  {renderFieldInput('播放器高度', 'twitter:player:height', ogData?.['twitter:player:height'], 'number')}
                </div>
              )}
            </TabsContent>

            <TabsContent value="facebook" className="space-y-4">
              {renderFieldInput('Facebook App ID', 'fb:app_id', ogData?.['fb:app_id'], 'text', '123456789')}
              {renderFieldInput('Facebook 管理员ID', 'fb:admins', ogData?.['fb:admins'], 'text', '用逗号分隔多个ID')}
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                高级设置允许您添加自定义的 Open Graph 标签
              </div>
              <div className="space-y-2">
                <Label>自定义标签</Label>
                <div className="text-xs text-gray-500">
                  提示：对于常见的 Open Graph 标签，建议使用基础设置和其他标签页的选项
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
} 