"use client";

import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Database, Loader2, AlertCircle } from 'lucide-react';
import { Project } from '@/types/project';

interface ProjectSelectorProps {
  selectedProjectId?: number;
  onProjectChange: (projectId: number, project: Project) => void;
  className?: string;
}

export function ProjectSelector({ selectedProjectId, onProjectChange, className }: ProjectSelectorProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 缓存键名
  const CACHE_KEY = 'selected_project_id';

  // 从缓存中获取项目ID
  const getCachedProjectId = (): number | null => {
    try {
      const cached = localStorage.getItem(CACHE_KEY);
      return cached ? parseInt(cached) : null;
    } catch (error) {
      console.warn('获取缓存的项目ID失败:', error);
      return null;
    }
  };

  // 保存项目ID到缓存
  const setCachedProjectId = (projectId: number) => {
    try {
      localStorage.setItem(CACHE_KEY, projectId.toString());
    } catch (error) {
      console.warn('保存项目ID到缓存失败:', error);
    }
  };

  // 加载项目列表
  const loadProjects = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/projects');
      const result = await response.json();
      
      if (result.success && result.data) {
        const activeProjects = result.data.filter((p: Project) => p.isActive);
        setProjects(activeProjects);
        
        // 如果没有传入选中项目ID，则尝试从缓存或默认选择
        if (!selectedProjectId && activeProjects.length > 0) {
          const cachedProjectId = getCachedProjectId();
          let projectToSelect = activeProjects[0]; // 默认选择第一个
          
          // 如果缓存中有项目ID，尝试找到对应的项目
          if (cachedProjectId) {
            const cachedProject = activeProjects.find((p: Project) => p.id === cachedProjectId);
            if (cachedProject) {
              projectToSelect = cachedProject;
            }
          }
          
          onProjectChange(projectToSelect.id, projectToSelect);
        }
      } else {
        setError(result.error || '加载项目失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
      console.error('Load projects error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadProjects();
  }, []);

  // 监听外部传入的selectedProjectId变化，同步到缓存
  useEffect(() => {
    if (selectedProjectId && projects.length > 0) {
      const project = projects.find((p: Project) => p.id === selectedProjectId);
      if (project) {
        setCachedProjectId(selectedProjectId);
      }
    }
  }, [selectedProjectId, projects]);

  const handleProjectChange = (projectIdStr: string) => {
    const projectId = parseInt(projectIdStr);
    const project = projects.find((p: Project) => p.id === projectId);
    if (project) {
      // 保存选中的项目ID到缓存
      setCachedProjectId(projectId);
      onProjectChange(projectId, project);
    }
  };

  const selectedProject = projects.find(p => p.id === selectedProjectId);

  if (loading) {
    return (
      <div className={`flex items-center gap-2 p-3 border rounded-lg bg-muted/50 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">加载项目列表...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center gap-2 p-3 border rounded-lg bg-destructive/10 ${className}`}>
        <AlertCircle className="h-4 w-4 text-destructive" />
        <span className="text-sm text-destructive">{error}</span>
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className={`flex items-center gap-2 p-3 border rounded-lg bg-muted/50 ${className}`}>
        <Database className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">暂无可用项目</span>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center gap-2">
        {/* 展示logo或默认图标 */}
        {selectedProject?.logo ? (
          <img src={selectedProject.logo} alt="logo" className="h-4 w-4 rounded object-contain bg-white border" />
        ) : (
          <Database className="h-4 w-4 text-muted-foreground" />
        )}
        <span className="text-sm font-medium">选择项目:</span>
      </div>
      
      <Select value={selectedProjectId?.toString()} onValueChange={handleProjectChange}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder="请选择一个项目">
            {selectedProject && (
              <div className="flex items-center gap-2">
                {/* 展示logo或默认图标 */}
                {selectedProject.logo ? (
                  <img src={selectedProject.logo} alt="logo" className="h-4 w-4 rounded object-contain bg-white border" />
                ) : (
                  <Database className="h-4 w-4 text-muted-foreground" />
                )}
                <span>{selectedProject.name}</span>
                <Badge variant="secondary" className="text-xs">
                  {selectedProject.domain || selectedProject.dbName}
                </Badge>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {projects.map((project) => (
            <SelectItem key={project.id} value={project.id.toString()}>
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  {/* 展示logo或默认图标 */}
                  {project.logo ? (
                    <img src={project.logo} alt="logo" className="h-4 w-4 rounded object-contain bg-white border" />
                  ) : (
                    <Database className="h-4 w-4 text-muted-foreground" />
                  )}
                  <div className="flex flex-col">
                    <span className="font-medium">{project.name}</span>
                    <span className="text-xs text-muted-foreground">
                      {project.domain || project.dbName}
                    </span>
                  </div>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
} 