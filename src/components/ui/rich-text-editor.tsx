"use client";

import React, { use<PERSON><PERSON>back, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import {StarterKit} from '@tiptap/starter-kit';
import {Image} from '@tiptap/extension-image';
import {Link} from '@tiptap/extension-link';
import {Placeholder} from '@tiptap/extension-placeholder';
import {Color} from '@tiptap/extension-color';
import {TextStyle} from '@tiptap/extension-text-style';
import {Highlight} from '@tiptap/extension-highlight';
import {TextAlign} from '@tiptap/extension-text-align';
import {Table} from '@tiptap/extension-table';
import {TableRow} from '@tiptap/extension-table-row';
import {TableCell} from '@tiptap/extension-table-cell';
import {TableHeader} from '@tiptap/extension-table-header';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, Di<PERSON>Header, Di<PERSON><PERSON>it<PERSON>, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { uploadToS3 } from '@/lib/s3';
import { 
  Bold, 
  Italic, 
  Strikethrough,
  Code,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Link as LinkIcon,
  Image as ImageIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Table as TableIcon
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface RichTextEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  className?: string;
}

interface ImageUploadDialogProps {
  onInsert: (url: string, alt: string) => void;
}

function ImageUploadDialog({ onInsert }: ImageUploadDialogProps) {
  const [url, setUrl] = useState('');
  const [alt, setAlt] = useState('');
  const [uploading, setUploading] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [alertInfo, setAlertInfo] = useState<{ message: string; type?: 'default' | 'destructive' } | null>(null);

  const handleFileUpload = async () => {
    if (!file) return;

    setUploading(true);
    try {
      const result = await uploadToS3(file, {
        folder: 'blog-images',
        contentType: file.type,
      });
      setUrl(result.url);
    } catch (error) {
      setAlertInfo({ message: '图片上传失败，请重试'+error, type: 'destructive' });
    } finally {
      setUploading(false);
    }
  };

  const handleInsert = () => {
    if (url) {
      onInsert(url, alt);
      setUrl('');
      setAlt('');
      setFile(null);
    }
  };

  return (
    <DialogContent className="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>插入图片</DialogTitle>
      </DialogHeader>
      <div className="space-y-4">
        <div>
          <Label htmlFor="image-file">上传图片</Label>
          <Input
            id="image-file"
            type="file"
            accept="image/*"
            onChange={(e) => setFile(e.target.files?.[0] || null)}
          />
          {file && (
            <Button 
              onClick={handleFileUpload} 
              disabled={uploading}
              className="mt-2 w-full"
            >
              {uploading ? '上传中...' : '上传到S3'}
            </Button>
          )}
        </div>
        
        <Separator />
        
        <div>
          <Label htmlFor="image-url">或输入图片URL</Label>
          <Input
            id="image-url"
            placeholder="https://example.com/image.jpg"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
          />
        </div>
        
        <div>
          <Label htmlFor="image-alt">图片描述 (Alt 标签)</Label>
          <Input
            id="image-alt"
            placeholder="描述图片内容，用于无障碍访问"
            value={alt}
            onChange={(e) => setAlt(e.target.value)}
          />
        </div>
        
        <Button onClick={handleInsert} disabled={!url} className="w-full">
          插入图片
        </Button>
      </div>
      {alertInfo && (
        <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-full max-w-md">
          <Alert variant={alertInfo.type || 'default'}>
            <AlertDescription>{alertInfo.message}</AlertDescription>
          </Alert>
        </div>
      )}
    </DialogContent>
  );
}

interface LinkDialogProps {
  initialUrl?: string;
  onInsert: (url: string, text?: string) => void;
}

function LinkDialog({ initialUrl = '', onInsert }: LinkDialogProps) {
  const [url, setUrl] = useState(initialUrl);
  const [text, setText] = useState('');

  const handleInsert = () => {
    if (url) {
      onInsert(url, text || url);
      setUrl('');
      setText('');
    }
  };

  return (
    <DialogContent className="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>插入链接</DialogTitle>
      </DialogHeader>
      <div className="space-y-4">
        <div>
          <Label htmlFor="link-url">链接地址</Label>
          <Input
            id="link-url"
            placeholder="https://example.com"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
          />
        </div>
        
        <div>
          <Label htmlFor="link-text">链接文字</Label>
          <Input
            id="link-text"
            placeholder="链接描述文字"
            value={text}
            onChange={(e) => setText(e.target.value)}
          />
        </div>
        
        <Button onClick={handleInsert} disabled={!url} className="w-full">
          插入链接
        </Button>
      </div>
    </DialogContent>
  );
}

export function RichTextEditor({ 
  content = '', 
  onChange,
  placeholder = '开始写作...',
  className 
}: RichTextEditorProps) {
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      TextStyle,
      Highlight.configure({
        multicolor: true,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
    ],
    content,
    immediatelyRender: false, // 禁用立即渲染以避免 SSR 水合不匹配
    onUpdate: ({ editor }) => {
      onChange?.(JSON.stringify(editor.getJSON()));
    },
  });

  const insertImage = useCallback((url: string, alt: string) => {
    if (editor) {
      editor.chain().focus().setImage({ src: url, alt }).run();
      setIsImageDialogOpen(false);
    }
  }, [editor]);

  const insertLink = useCallback((url: string, text?: string) => {
    if (editor) {
      if (text) {
        editor.chain().focus().insertContent(`<a href="${url}">${text}</a>`).run();
      } else {
        editor.chain().focus().setLink({ href: url }).run();
      }
      setIsLinkDialogOpen(false);
    }
  }, [editor]);

  const insertTable = useCallback(() => {
    if (editor) {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
    }
  }, [editor]);

  if (!editor) {
    return null;
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* 工具栏 */}
      <div className="border-b p-2 flex flex-wrap gap-1">
        {/* 文本格式 */}
        <Button
          variant={editor.isActive('bold') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
        >
          <Bold className="h-4 w-4" />
        </Button>
        
        <Button
          variant={editor.isActive('italic') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
        >
          <Italic className="h-4 w-4" />
        </Button>
        
        <Button
          variant={editor.isActive('strike') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleStrike().run()}
        >
          <Strikethrough className="h-4 w-4" />
        </Button>
        
        <Button
          variant={editor.isActive('code') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleCode().run()}
        >
          <Code className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-1 h-8" />

        {/* 标题 */}
        <Button
          variant={editor.isActive('heading', { level: 1 }) ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
        >
          <Heading1 className="h-4 w-4" />
        </Button>
        
        <Button
          variant={editor.isActive('heading', { level: 2 }) ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
        >
          <Heading2 className="h-4 w-4" />
        </Button>
        
        <Button
          variant={editor.isActive('heading', { level: 3 }) ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
        >
          <Heading3 className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-1 h-8" />

        {/* 列表 */}
        <Button
          variant={editor.isActive('bulletList') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleBulletList().run()}
        >
          <List className="h-4 w-4" />
        </Button>
        
        <Button
          variant={editor.isActive('orderedList') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
        >
          <ListOrdered className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-1 h-8" />

        {/* 对齐 */}
        <Button
          variant={editor.isActive({ textAlign: 'left' }) ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('left').run()}
        >
          <AlignLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant={editor.isActive({ textAlign: 'center' }) ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('center').run()}
        >
          <AlignCenter className="h-4 w-4" />
        </Button>
        
        <Button
          variant={editor.isActive({ textAlign: 'right' }) ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().setTextAlign('right').run()}
        >
          <AlignRight className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-1 h-8" />

        {/* 引用 */}
        <Button
          variant={editor.isActive('blockquote') ? 'default' : 'outline'}
          size="sm"
          onClick={() => editor.chain().focus().toggleBlockquote().run()}
        >
          <Quote className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-1 h-8" />

        {/* 媒体和链接 */}
        <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <ImageIcon className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <ImageUploadDialog onInsert={insertImage} />
        </Dialog>
        
        <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <LinkIcon className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <LinkDialog onInsert={insertLink} />
        </Dialog>

        <Button
          variant="outline"
          size="sm"
          onClick={insertTable}
        >
          <TableIcon className="h-4 w-4" />
        </Button>

        <Separator orientation="vertical" className="mx-1 h-8" />

        {/* 撤销重做 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => editor.chain().focus().undo().run()}
          disabled={!editor.can().undo()}
        >
          <Undo className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => editor.chain().focus().redo().run()}
          disabled={!editor.can().redo()}
        >
          <Redo className="h-4 w-4" />
        </Button>
      </div>

      {/* 编辑器内容区域 */}
      <EditorContent 
        editor={editor} 
        className="p-4 min-h-[300px] focus:outline-none"
      />
    </div>
  );
} 