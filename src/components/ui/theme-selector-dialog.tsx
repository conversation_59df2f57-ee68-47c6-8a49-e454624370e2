"use client";

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, ChevronLeft, ChevronRight, Check } from 'lucide-react';
import { OptImage } from './custom-image';

interface ThemeStyles {
  [styleName: string]: string[];
}

interface ThemeSelectorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: number;
  onSave?: () => void;
}

export function ThemeSelectorDialog({
  open,
  onOpenChange,
  projectId,
  onSave
}: ThemeSelectorDialogProps) {
  const [themeStyles, setThemeStyles] = useState<ThemeStyles>({});
  const [selectedStyle, setSelectedStyle] = useState<string>('');
  const [selectedImageIndex, setSelectedImageIndex] = useState<Record<string, number>>({});
  const [currentSelectedTheme, setCurrentSelectedTheme] = useState<{style: string, imageUrl: string} | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
 
  // 加载主题样式数据
  const loadThemeStyles = async () => {
    if (!open) return;
    
    setLoading(true);
    try {
      // 同时加载主题样式和当前配置
      const [stylesResponse, configResponse] = await Promise.all([
        fetch('/api/system/config?key=blog-theme-styles'),
        fetch(`/api/content/site-config?projectId=${projectId}&keys=blog-theme`)
      ]);

      let currentTheme = null;
      if (configResponse.ok) {
        const configResult = await configResponse.json();
        if (configResult.success && configResult.data['blog-theme']) {
          currentTheme = JSON.parse(configResult.data['blog-theme'].value);
          setCurrentSelectedTheme(currentTheme);
        }
      }

      if (stylesResponse.ok) {
        const result = await stylesResponse.json();
        if (result.success && result.data.value) {
          const styles = result.data.value as ThemeStyles;
          setThemeStyles(styles);

          // 初始化每个风格的图片索引
          const initialIndexes: Record<string, number> = {};
          Object.keys(styles).forEach(style => {
            initialIndexes[style] = 0;
          });

          // 如果有当前主题配置，定位到对应的风格和图片
          if (currentTheme && currentTheme.style && currentTheme.imageUrl) {
            const { style: currentStyle, imageUrl: currentImageUrl } = currentTheme;
            
            // 检查当前风格是否存在
            if (styles[currentStyle]) {
              setSelectedStyle(currentStyle);
              
              // 找到当前图片在风格数组中的索引
              const imageIndex = styles[currentStyle].findIndex(url => url === currentImageUrl);
              if (imageIndex !== -1) {
                initialIndexes[currentStyle] = imageIndex;
              }
            } else {
              // 如果当前风格不存在，使用第一个风格
              const firstStyle = Object.keys(styles)[0];
              if (firstStyle) {
                setSelectedStyle(firstStyle);
              }
            }
          } else {
            // 没有当前主题配置，使用第一个风格
            const firstStyle = Object.keys(styles)[0];
            if (firstStyle) {
              setSelectedStyle(firstStyle);
            }
          }

          setSelectedImageIndex(initialIndexes);
        }
      }
    } catch (error) {
      console.error('加载主题样式失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 切换图片
  const navigateImage = (style: string, direction: 'prev' | 'next') => {
    const currentIndex = selectedImageIndex[style] || 0;
    const images = themeStyles[style] || [];
    let newIndex;
    
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : currentIndex;
    } else {
      newIndex = currentIndex < images.length - 1 ? currentIndex + 1 : currentIndex;
    }
    
    setSelectedImageIndex(prev => ({
      ...prev,
      [style]: newIndex
    }));
  };

  // 选择主题
  const selectTheme = (style: string, imageUrl: string) => {
    setCurrentSelectedTheme({ style, imageUrl });
  };

  // 保存主题配置
  const handleSave = async () => {
    if (!currentSelectedTheme) {
      alert('请先选择一个主题');
      return;
    }

    setSaving(true);
    try {
      const response = await fetch('/api/content/site-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          key: 'blog-theme',
          value: JSON.stringify(currentSelectedTheme),
          type: 'string',
          category: 'theme',
          description: '博客主题配置',
          isPublic: true,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          onOpenChange(false);
          onSave?.();
        } else {
          alert('保存失败: ' + result.error);
        }
      } else {
        alert('保存失败');
      }
    } catch (error) {
      console.error('保存主题配置失败:', error);
      alert('保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 当弹窗打开时加载数据
  useEffect(() => {
    loadThemeStyles();
  }, [open, projectId]);

  const styleNames = Object.keys(themeStyles);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="!w-[50vw] !h-[80vh] !max-w-none p-0">
        <div className="flex flex-col h-full">
          {/* 头部 - 固定高度 */}
          <DialogHeader className="flex-shrink-0 p-6 pb-4 border-b">
            <DialogTitle>博客主题选择</DialogTitle>
            <DialogDescription>选择适合您网站的博客主题样式</DialogDescription>
          </DialogHeader>

          {loading ? (
            <div className="flex items-center justify-center flex-1">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">加载主题数据中...</span>
            </div>
          ) : (
            <>
              {/* 主要内容区域 */}
              <div className="flex-1 p-6">
                {/* 风格切换选项卡 */}
                <Tabs value={selectedStyle} onValueChange={setSelectedStyle} className="h-full flex flex-col">
                  <TabsList className={`grid w-full mb-4 flex-shrink-0 ${styleNames.length <= 2 ? 'grid-cols-2' : styleNames.length === 3 ? 'grid-cols-3' : 'grid-cols-4'}`}>
                    {styleNames.map((style) => (
                      <TabsTrigger key={style} value={style} className="capitalize">
                        {style}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  {styleNames.map((style) => (
                    <TabsContent key={style} value={style} className="flex-1 flex flex-col ">
                      <div className="flex flex-col h-full">
                        {/* 图片展示区域 - 占用大部分空间 */}
                        <div className="relative flex-1 rounded-lg max-h-[700px] mb-4">
                          {themeStyles[style] && themeStyles[style].length > 0 && (
                            <>
                              <div className="relative w-full min-h-[400px] max-h-[600px] overflow-y-auto">
                                <OptImage
                                  src={themeStyles[style][selectedImageIndex[style] || 0]}
                                  alt={`${style} theme preview`}
                                  className="object-contain"
                                  width={1080}
                                  height={50}
                                />
                              </div>
                              
                              {/* 导航箭头 - 只有多张图片时显示 */}
                              {themeStyles[style].length > 1 && (
                                <>
                                  {selectedImageIndex[style] !=0 && (
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white shadow-lg"
                                      onClick={() => navigateImage(style, 'prev')}
                                    >
                                      <ChevronLeft className="h-4 w-4" />
                                    </Button>
                                  )}
                                  
                                  {selectedImageIndex[style] !=themeStyles[style].length - 1 && (
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white shadow-lg"
                                      onClick={() => navigateImage(style, 'next')}
                                    >
                                      <ChevronRight className="h-4 w-4" />
                                    </Button>
                                  )}
                
                                </>
                              )}
                            </>
                          )}
                        </div>

                        {/* 选择按钮 - 固定高度 */}
                        <div className="flex justify-center flex-shrink-0">
                          <Button
                            onClick={() => selectTheme(style, themeStyles[style][selectedImageIndex[style] || 0])}
                            variant={
                              currentSelectedTheme?.style === style && 
                              currentSelectedTheme?.imageUrl === themeStyles[style][selectedImageIndex[style] || 0]
                                ? "default" 
                                : "outline"
                            }
                            className="flex items-center gap-2"
                          >
                            {currentSelectedTheme?.style === style && 
                            currentSelectedTheme?.imageUrl === themeStyles[style][selectedImageIndex[style] || 0] && (
                              <Check className="h-4 w-4" />
                            )}
                            选择这个主题
                          </Button>
                        </div>
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </div>

              {/* 底部按钮 - 固定高度 */}
              <div className="flex justify-end space-x-2 p-6 pt-4 border-t flex-shrink-0">
                <Button
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={saving}
                >
                  取消
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={saving || !currentSelectedTheme}
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      保存中...
                    </>
                  ) : (
                    '保存主题'
                  )}
                </Button>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 