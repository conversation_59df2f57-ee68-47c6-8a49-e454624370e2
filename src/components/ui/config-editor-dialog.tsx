"use client";

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2, Save } from 'lucide-react';

interface ConfigEditorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  configKey: string;
  title: string;
  description: string;
  projectId: number;
  domain?: string;
  onSave?: () => void;
}

export function ConfigEditorDialog({
  open,
  onOpenChange,
  configKey,
  title,
  description,
  projectId,
  domain = '',
  onSave
}: ConfigEditorDialogProps) {
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 获取预设内容
  const getDefaultContent = (key: string): string => {
    switch (key) {
      case 'robots.txt':
        let content = '';
        if (domain && domain.includes('test')) {
          content += 'User-agent: *\nDisallow: /\n';
        } else {
          content += 'User-agent: *\nAllow: /\n';
        }
        content += `
Host: ${domain ? `https://${domain}` : 'https://example.com'}
Sitemap: ${domain ? `https://${domain}/sitemap.xml` : 'https://example.com/sitemap.xml'}
# AI 爬虫识别字段说明（如有禁止需求，可在下方增加 Disallow）
# OpenAI – https://platform.openai.com/docs/bots
User-agent: GPTBot
User-agent: ChatGPT-User
User-agent: OAI-SearchBot

# Anthropic – https://support.anthropic.com/en/articles/8896518
User-agent: ClaudeBot
User-agent: Claude-User
User-agent: Claude-SearchBot

# Google (Gemini) – https://blog.google/technology/ai/an-update-on-web-publisher-controls/
User-agent: Google-Extended

# Microsoft (Bing / Copilot) – https://www.bing.com/webmasters/help/which-crawlers-does-bing-use-8c184ec0
User-agent: BingBot

# Meta (Llama / Meta AI) – https://developers.facebook.com/docs/sharing/webmasters/web-crawlers/
User-agent: meta-externalagent
User-agent: FacebookBot

# Amazon (Titan / Alexa) – https://developer.amazon.com/amazonbot
User-agent: Amazonbot

# Apple – https://support.apple.com/en-us/119829
User-agent: Applebot
User-agent: Applebot-Extended

# Perplexity – https://docs.perplexity.ai/guides/bots
User-agent: PerplexityBot
User-agent: Perplexity-User

# Cohere – *官方尚未发布专门 UA 页面（2025‑05）；建议持续关注 https://cohere.ai/*
User-agent: cohere-ai

# Mistral – https://docs.mistral.ai/robots/
User-agent: MistralAI-User

# Common Crawl – https://commoncrawl.org/ccbot
User-agent: CCBot
`;
        return content;

      case 'llms.txt':
        return `# LLMs.txt
# Dolphin Radar
> **Dolphin Radar** is a leading Instagram tracking and analytics tool designed for marketers and individual users. With over 50,000 active users, our platform allows you to view Instagram stories, profiles, comments, and interactions without leaving a trace, while delivering in-depth analytical reports that reveal hidden interactions and behavior patterns. 

## Core Features
- [Instagram Activity Analysis Report](https://www.dolphinradar.com/): Track and analyze any public Instagram account. Reports include peak activity times, top interactions, recently liked posts, user interests, follower growth trends, and more.  
- [Instagram Recent Follow Tracker](https://www.dolphinradar.com/instagram-new-follow-tracker): Check who a public Instagram account recently followed and who just followed them — all for free.  
- [Instagram Unfollowers Tracker](https://www.dolphinradar.com/instagram-unfollowers-tracker): Discover who unfollowed a public Instagram account. Free access with no login required.  
- [Instagram Post Viewer](https://www.dolphinradar.com/web-viewer-for-instagram): View and download public Instagram profiles and posts easily and anonymously.  
- [Instagram Story Viewer](https://www.dolphinradar.com/anonymous-instagram-story-viewer): Watch and download stories from any public Instagram account without revealing your identity.  
- [Instagram Comment Viewer](https://www.dolphinradar.com/instagram-viewer/post-comments): View post comments on any public Instagram content, including commenter names, timestamps, and full comment text.  
- [Instagram Likes Viewer](https://www.dolphinradar.com/instagram-viewer/post-likes): View likes on public Instagram posts, sorted by most recent activity — completely free and easy to use.  
- [Instagram Hashtag Generator](https://www.dolphinradar.com/instagram-hashtag-generator): Generate effective and high-performing Instagram hashtags for free to help boost your growth and visibility.  
- [Instagram Influencer Analyzer](https://www.dolphinradar.com/top-instagram): Analyze Instagram influencers' account data, including activity insights, follower growth trends, and trending topics.

## Key Advantages
- **Fully Anonymous**: The accounts you view will not know they are being analyzed.  
- **Fully Automated**: All data collection and analysis are handled by DolphinRadar, saving users time and effort.  
- **Comprehensive Reports**: From basic profile details to deep behavioral insights, DolphinRadar delivers full-spectrum analysis.  
- **AI Powered**: Leverages advanced AI technologies to generate powerful data insights and personalized recommendations.

## User Scenarios
**DolphinRadar serves multiple user needs:**

### For Marketing
- Analyze Instagram competitors by tracking follower growth, content strategy, and engagement metrics.  
- Help brands discover the right Instagram influencers for campaigns and collaborations.

### For Individuals
- View public Instagram profiles, including profile, posts, and Stories in one place.  
- Uncover hidden patterns behind Instagram activity, such as relationship networks and personal interests.

## Common Resources
- [Toolkits](https://www.dolphinradar.com/toolkit): A list of Instagram tools offered by DolphinRadar  
- [Help Center](https://www.dolphinradar.com/instagram-tracking-faq): FAQs about account settings, subscriptions, and features  
- [Pricing](https://www.dolphinradar.com/plan): Our Plans & Pricing`;

      default:
        return '';
    }
  };

  // 加载现有配置
  const loadConfig = async () => {
    if (!open || !projectId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/content/site-config?projectId=${projectId}&keys=${configKey}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data[configKey]) {
          setContent(result.data[configKey].value || '');
        } else {
          // 如果配置不存在，使用默认内容
          setContent(getDefaultContent(configKey));
        }
      } else {
        // 加载失败，使用默认内容
        setContent(getDefaultContent(configKey));
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      setContent(getDefaultContent(configKey));
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const handleSave = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/content/site-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId,
          key: configKey,
          value: content,
          type: 'string',
          category: 'basic',
          description: description,
          isPublic: true,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          onOpenChange(false);
          onSave?.();
        } else {
          alert('保存失败: ' + result.error);
        }
      } else {
        alert('保存失败');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      alert('保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 当弹窗打开时加载配置
  useEffect(() => {
    loadConfig();
  }, [open, projectId, configKey]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl w-[90vw] max-h-[90vh] h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">加载配置中...</span>
          </div>
        ) : (
          <div className="flex flex-col space-y-4 flex-1 overflow-hidden">
            <div className="flex-1 flex flex-col overflow-hidden">
              <Label htmlFor="content" className="mb-2">内容</Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={`请输入${title}的内容...`}
                className="flex-1 font-mono text-sm resize-none overflow-auto"
              />
            </div>

            <div className="flex justify-end space-x-2 flex-shrink-0 pt-2 border-t">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={saving}
              >
                取消
              </Button>
              <Button
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    保存中...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    保存
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
} 