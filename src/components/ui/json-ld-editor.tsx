"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Plus,
  Trash2,
  Code,
  Eye,
  AlertCircle,
  Check
} from 'lucide-react';
import { JsonLdData } from '@/types/page-meta';

interface JsonLdEditorProps {
  value: JsonLdData | null;
  onChange: (value: JsonLdData | null) => void;
  disabled?: boolean;
}

// JSON-LD类型模板
const JSON_LD_TEMPLATES = {
  Article: {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: '',
    description: '',
    author: {
      '@type': 'Person',
      name: '',
    },
    datePublished: '',
    dateModified: '',
    image: '',
    url: '',
  },
  Website: {
    '@context': 'https://schema.org',
    '@type': 'Website',
    name: '',
    description: '',
    url: '',
    author: {
      '@type': 'Organization',
      name: '',
    },
  },
  Organization: {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: '',
    description: '',
    url: '',
    logo: '',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '',
      contactType: 'customer service',
    },
  },
  Person: {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: '',
    description: '',
    url: '',
    image: '',
    jobTitle: '',
    worksFor: {
      '@type': 'Organization',
      name: '',
    },
  },
  Product: {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: '',
    description: '',
    image: '',
    offers: {
      '@type': 'Offer',
      price: '',
      priceCurrency: 'CNY',
      availability: 'https://schema.org/InStock',
    },
  },
  Event: {
    '@context': 'https://schema.org',
    '@type': 'Event',
    name: '',
    description: '',
    startDate: '',
    endDate: '',
    location: {
      '@type': 'Place',
      name: '',
      address: '',
    },
  },
  FAQ: {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: [],
  },
  BreadcrumbList: {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [],
  }
};

// 定义通用的 JSON-LD 对象类型
export type JsonLdObject = Record<string, unknown>;

// FAQ 问题类型
interface FaqQuestion {
  '@type': 'Question';
  name: string;
  acceptedAnswer: {
    '@type': 'Answer';
    text: string;
  };
}

// BreadcrumbList 项类型
interface BreadcrumbItem {
  '@type': 'ListItem';
  position: number;
  name: string;
  item: string;
}

export default function JsonLdEditor({ value, onChange, disabled = false }: JsonLdEditorProps) {
  const [jsonLdData, setJsonLdData] = useState<JsonLdData | null>(value);
  const [selectedType, setSelectedType] = useState<string>('Article');
  const [isRawMode, setIsRawMode] = useState(false);
  const [rawJson, setRawJson] = useState('');
  const [jsonError, setJsonError] = useState<string | null>(null);

  useEffect(() => {
    setJsonLdData(value);
    if (value) {
      setSelectedType(value['@type'] || 'Article');
      setRawJson(JSON.stringify(value, null, 2));
    } else {
      setRawJson('');
    }
  }, [value]);

  const handleTypeChange = (type: string) => {
    setSelectedType(type);
    const template = JSON_LD_TEMPLATES[type as keyof typeof JSON_LD_TEMPLATES];
    if (template) {
      const newData = { ...template };
      setJsonLdData(newData);
      setRawJson(JSON.stringify(newData, null, 2));
      onChange(newData);
    }
  };

  const handleFieldChange = (path: string, value: unknown) => {
    if (!jsonLdData) return;

    const newData = { ...jsonLdData };
    const keys = path.split('.');
    let current = newData;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current)) {
        current[key] = {};
      }
      current = current[key];
    }

    current[keys[keys.length - 1]] = value;
    setJsonLdData(newData);
    setRawJson(JSON.stringify(newData, null, 2));
    onChange(newData);
  };

  const handleRawJsonChange = (newJson: string) => {
    setRawJson(newJson);
    setJsonError(null);

    try {
      if (newJson.trim() === '') {
        setJsonLdData(null);
        onChange(null);
        return;
      }

      const parsed = JSON.parse(newJson);
      setJsonLdData(parsed);
      onChange(parsed);
    } catch {
      setJsonError('JSON格式错误');
    }
  };

  const addArrayItem = (path: string, template: JsonLdObject) => {
    if (!jsonLdData) return;

    const newData = { ...jsonLdData };
    const keys = path.split('.');
    let current = newData;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current)) {
        current[key] = {};
      }
      current = current[key];
    }

    const arrayKey = keys[keys.length - 1];
    if (!Array.isArray(current[arrayKey])) {
      current[arrayKey] = [];
    }

    current[arrayKey].push(template);
    setJsonLdData(newData);
    setRawJson(JSON.stringify(newData, null, 2));
    onChange(newData);
  };

  const removeArrayItem = (path: string, index: number) => {
    if (!jsonLdData) return;

    const newData = { ...jsonLdData };
    const keys = path.split('.');
    let current = newData;

    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      current = current[key];
    }

    const arrayKey = keys[keys.length - 1];
    if (Array.isArray(current[arrayKey])) {
      current[arrayKey].splice(index, 1);
    }

    setJsonLdData(newData);
    setRawJson(JSON.stringify(newData, null, 2));
    onChange(newData);
  };

  const clearJsonLd = () => {
    setJsonLdData(null);
    setRawJson('');
    onChange(null);
  };

  const renderFieldInput = (label: string, path: string, value: unknown, type: string = 'text') => {
    return (
      <div className="space-y-2">
        <Label htmlFor={path}>{label}</Label>
        {type === 'textarea' ? (
          <Textarea
            id={path}
            value={typeof value === 'string' ? value : value === undefined || value === null ? '' : String(value)}
            onChange={(e) => handleFieldChange(path, e.target.value)}
            disabled={disabled}
            rows={3}
          />
        ) : (
          <Input
            id={path}
            type={type}
            value={typeof value === 'string' ? value : value === undefined || value === null ? '' : String(value)}
            onChange={(e) => handleFieldChange(path, e.target.value)}
            disabled={disabled}
          />
        )}
      </div>
    );
  };

  const renderTypeSpecificFields = () => {
    if (!jsonLdData) return null;

    const type = jsonLdData['@type'];

    switch (type) {
      case 'Article':
        return (
          <div className="space-y-4">
            {renderFieldInput('文章标题', 'headline', jsonLdData.headline)}
            {renderFieldInput('文章描述', 'description', jsonLdData.description, 'textarea')}
            {renderFieldInput('作者姓名', 'author.name', jsonLdData.author?.name)}
            {renderFieldInput('发布日期', 'datePublished', jsonLdData.datePublished, 'datetime-local')}
            {renderFieldInput('修改日期', 'dateModified', jsonLdData.dateModified, 'datetime-local')}
            {renderFieldInput('文章图片', 'image', jsonLdData.image)}
            {renderFieldInput('文章URL', 'url', jsonLdData.url)}
          </div>
        );

      case 'Website':
        return (
          <div className="space-y-4">
            {renderFieldInput('网站名称', 'name', jsonLdData.name)}
            {renderFieldInput('网站描述', 'description', jsonLdData.description, 'textarea')}
            {renderFieldInput('网站URL', 'url', jsonLdData.url)}
            {renderFieldInput('组织名称', 'author.name', jsonLdData.author?.name)}
          </div>
        );

      case 'Organization':
        return (
          <div className="space-y-4">
            {renderFieldInput('组织名称', 'name', jsonLdData.name)}
            {renderFieldInput('组织描述', 'description', jsonLdData.description, 'textarea')}
            {renderFieldInput('组织URL', 'url', jsonLdData.url)}
            {renderFieldInput('Logo图片', 'logo', jsonLdData.logo)}
            {renderFieldInput('联系电话', 'contactPoint.telephone', jsonLdData.contactPoint?.telephone)}
            {renderFieldInput('联系类型', 'contactPoint.contactType', jsonLdData.contactPoint?.contactType)}
          </div>
        );

      case 'Person':
        return (
          <div className="space-y-4">
            {renderFieldInput('姓名', 'name', jsonLdData.name)}
            {renderFieldInput('个人描述', 'description', jsonLdData.description, 'textarea')}
            {renderFieldInput('个人URL', 'url', jsonLdData.url)}
            {renderFieldInput('头像图片', 'image', jsonLdData.image)}
            {renderFieldInput('职位', 'jobTitle', jsonLdData.jobTitle)}
            {renderFieldInput('工作单位', 'worksFor.name', jsonLdData.worksFor?.name)}
          </div>
        );

      case 'Product':
        return (
          <div className="space-y-4">
            {renderFieldInput('产品名称', 'name', jsonLdData.name)}
            {renderFieldInput('产品描述', 'description', jsonLdData.description, 'textarea')}
            {renderFieldInput('产品图片', 'image', jsonLdData.image)}
            {renderFieldInput('价格', 'offers.price', jsonLdData.offers?.price)}
            {renderFieldInput('货币', 'offers.priceCurrency', jsonLdData.offers?.priceCurrency)}
            {renderFieldInput('可用性', 'offers.availability', jsonLdData.offers?.availability)}
          </div>
        );

      case 'Event':
        return (
          <div className="space-y-4">
            {renderFieldInput('活动名称', 'name', jsonLdData.name)}
            {renderFieldInput('活动描述', 'description', jsonLdData.description, 'textarea')}
            {renderFieldInput('开始时间', 'startDate', jsonLdData.startDate, 'datetime-local')}
            {renderFieldInput('结束时间', 'endDate', jsonLdData.endDate, 'datetime-local')}
            {renderFieldInput('活动地点', 'location.name', jsonLdData.location?.name)}
            {renderFieldInput('地点地址', 'location.address', jsonLdData.location?.address)}
          </div>
        );

      case 'FAQ':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>常见问题</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addArrayItem('mainEntity', {
                  '@type': 'Question',
                  name: '',
                  acceptedAnswer: {
                    '@type': 'Answer',
                    text: '',
                  },
                })}
                disabled={disabled}
              >
                <Plus className="h-4 w-4 mr-2" />
                添加问题
              </Button>
            </div>
            {Array.isArray(jsonLdData.mainEntity) && jsonLdData.mainEntity.map((item: FaqQuestion, index: number) => (
              <Card key={index} className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <Label>问题 {index + 1}</Label>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeArrayItem('mainEntity', index)}
                    disabled={disabled}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-2">
                  <Input
                    placeholder="问题内容"
                    value={item.name || ''}
                    onChange={(e) => handleFieldChange(`mainEntity.${index}.name`, e.target.value)}
                    disabled={disabled}
                  />
                  <Textarea
                    placeholder="答案内容"
                    value={item.acceptedAnswer?.text || ''}
                    onChange={(e) => handleFieldChange(`mainEntity.${index}.acceptedAnswer.text`, e.target.value)}
                    disabled={disabled}
                    rows={2}
                  />
                </div>
              </Card>
            ))}
          </div>
        );

      case 'BreadcrumbList':
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>面包屑导航</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addArrayItem('itemListElement', {
                  '@type': 'ListItem',
                  position: (jsonLdData.itemListElement?.length || 0) + 1,
                  name: '',
                  item: '',
                })}
                disabled={disabled}
              >
                <Plus className="h-4 w-4 mr-2" />
                添加面包屑
              </Button>
            </div>
            {Array.isArray(jsonLdData.itemListElement) && jsonLdData.itemListElement.map((item: BreadcrumbItem, index: number) => (
              <Card key={index} className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <Label>面包屑 {index + 1}</Label>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeArrayItem('itemListElement', index)}
                    disabled={disabled}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-2">
                  <Input
                    placeholder="显示名称"
                    value={item.name || ''}
                    onChange={(e) => handleFieldChange(`itemListElement.${index}.name`, e.target.value)}
                    disabled={disabled}
                  />
                  <Input
                    placeholder="链接地址"
                    value={item.item || ''}
                    onChange={(e) => handleFieldChange(`itemListElement.${index}.item`, e.target.value)}
                    disabled={disabled}
                  />
                </div>
              </Card>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code className="h-5 w-5" />
          JSON-LD 结构化数据
        </CardTitle>
        <CardDescription>
          配置页面的结构化数据，帮助搜索引擎更好地理解页面内容
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 类型选择和模式切换 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div>
              <Label htmlFor="jsonld-type">结构化数据类型</Label>
              <Select value={selectedType} onValueChange={handleTypeChange} disabled={disabled}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Article">文章</SelectItem>
                  <SelectItem value="Website">网站</SelectItem>
                  <SelectItem value="Organization">组织</SelectItem>
                  <SelectItem value="Person">人物</SelectItem>
                  <SelectItem value="Product">产品</SelectItem>
                  <SelectItem value="Event">活动</SelectItem>
                  <SelectItem value="FAQ">常见问题</SelectItem>
                  <SelectItem value="BreadcrumbList">面包屑导航</SelectItem>
                
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant={isRawMode ? "outline" : "default"}
                size="sm"
                onClick={() => setIsRawMode(false)}
                disabled={disabled}
              >
                <Eye className="h-4 w-4 mr-2" />
                可视化
              </Button>
              <Button
                type="button"
                variant={isRawMode ? "default" : "outline"}
                size="sm"
                onClick={() => setIsRawMode(true)}
                disabled={disabled}
              >
                <Code className="h-4 w-4 mr-2" />
                代码
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {jsonLdData && !jsonError && (
              <Badge variant="outline" className="text-green-600">
                <Check className="h-3 w-3 mr-1" />
                有效
              </Badge>
            )}
            {jsonError && (
              <Badge variant="destructive">
                <AlertCircle className="h-3 w-3 mr-1" />
                错误
              </Badge>
            )}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={clearJsonLd}
              disabled={disabled}
            >
              清空
            </Button>
          </div>
        </div>

        {/* 内容区域 */}
        {isRawMode ? (
          <div className="space-y-2">
            <Label htmlFor="raw-json">JSON-LD 代码</Label>
            <Textarea
              id="raw-json"
              value={rawJson}
              onChange={(e) => handleRawJsonChange(e.target.value)}
              placeholder="输入 JSON-LD 代码..."
              rows={15}
              className="font-mono text-sm"
              disabled={disabled}
            />
            {jsonError && (
              <p className="text-sm text-destructive flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                {jsonError}
              </p>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {jsonLdData ? (
              renderTypeSpecificFields()
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Code className="h-12 w-12 mx-auto mb-4" />
                <p>选择结构化数据类型来开始配置</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 