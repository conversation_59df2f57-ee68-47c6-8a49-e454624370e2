"use client";

import React from 'react';
import { FaqRichTextContent } from '@/types/faq';

interface FaqRichTextDisplayProps {
  content: string; // JSON string from database
  className?: string;
  maxLength?: number; // For truncation in list views
}

export function FaqRichTextDisplay({ 
  content, 
  className = '',
  maxLength
}: FaqRichTextDisplayProps) {
  // Parse the JSON content
  let parsedContent: FaqRichTextContent;
  try {
    parsedContent = JSON.parse(content);
  } catch {
    // Fallback for plain text content - convert to simple paragraph
    if (maxLength && content.length > maxLength) {
      const truncatedText = content.substring(0, maxLength) + '...';
      return <div className={className}>{truncatedText}</div>;
    }
    return <div className={className}>{content}</div>;
  }

  // Convert Tiptap JSON to React elements
  const renderContent = (node: FaqRichTextContent, key: number = 0): React.ReactNode => {
    if (node.type === 'text') {
      let textElement: React.ReactNode = node.text || '';
      
      // Apply marks (formatting)
      if (node.marks) {
        node.marks.forEach(mark => {
          switch (mark.type) {
            case 'bold':
              textElement = <strong key={`bold-${key}`}>{textElement}</strong>;
              break;
            case 'underline':
              textElement = <u key={`underline-${key}`}>{textElement}</u>;
              break;
            case 'strike':
              textElement = <s key={`strike-${key}`}>{textElement}</s>;
              break;
            case 'link':
              textElement = (
                <a
                  key={`link-${key}`}
                  href={typeof mark.attrs?.href === 'string' ? mark.attrs.href : '#'}
                  className="text-blue-600 hover:text-blue-800 underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {textElement}
                </a>
              );
              break;
          }
        });
      }
      
      return textElement;
    }

    // Handle different node types
    switch (node.type) {
      case 'doc':
        return (
          <div key={key}>
            {node.content?.map((child, index) => renderContent(child, index))}
          </div>
        );
      
      case 'paragraph':
        return (
          <p key={key} className="mb-2 last:mb-0">
            {node.content?.map((child, index) => renderContent(child, index))}
          </p>
        );
      
      default:
        // For any other node types, just render their content
        return (
          <span key={key}>
            {node.content?.map((child, index) => renderContent(child, index))}
          </span>
        );
    }
  };

  // Extract plain text for truncation
  const extractPlainText = (node: FaqRichTextContent): string => {
    if (node.type === 'text') {
      return node.text || '';
    }
    
    if (node.content) {
      return node.content.map(child => extractPlainText(child)).join('');
    }
    
    return '';
  };

  // Handle truncation if maxLength is specified
  if (maxLength) {
    const plainText = extractPlainText(parsedContent);
    if (plainText.length > maxLength) {
      const truncatedText = plainText.substring(0, maxLength) + '...';
      return <div className={className}>{truncatedText}</div>;
    }
  }

  return (
    <div className={`prose prose-sm max-w-none ${className}`}>
      {renderContent(parsedContent)}
    </div>
  );
}

// Helper component for FAQ list items with consistent styling
export function FaqAnswerPreview({ 
  content, 
  className = "text-sm text-muted-foreground max-w-md line-clamp-2" 
}: { 
  content: string; 
  className?: string; 
}) {
  return (
    <FaqRichTextDisplay 
      content={content} 
      className={className}
      maxLength={150} // Limit preview length
    />
  );
}
