"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { 
  LayoutDashboard, 
  Users, 
  Settings, 
  Database, 
  FileText,
  Globe,
  Shield,
  Menu,
  LogOut
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

const menuItems = [
  {
    title: '概览',
    icon: LayoutDashboard,
    href: '/admin',
    description: '系统概览和统计信息'
  },
  {
    title: '业务项目',
    icon: Database,
    href: '/admin/projects',
    description: '管理业务项目和数据库连接'
  },
  {
    title: '用户管理',
    icon: Users,
    href: '/admin/users',
    description: '管理系统用户和权限'
  },
  {
    title: '角色权限',
    icon: Shield,
    href: '/admin/roles',
    description: '管理角色和权限配置'
  },
  {
    title: '菜单管理',
    icon: Menu,
    href: '/admin/menus',
    description: '管理系统菜单结构'
  },
  {
    title: '内容管理',
    icon: FileText,
    href: '/admin/content',
    children: [
      { title: 'Meta配置', href: '/admin/content/meta' },
      { title: '博客管理', href: '/admin/content/blog' },
      { title: 'FAQ管理', href: '/admin/content/faq' },
      { title: '文件管理', href: '/admin/content/files' },
    ]
  },
  {
    title: '站点配置',
    icon: Globe,
    href: '/admin/site-config',
    description: '网站基础配置和设置'
  },
  {
    title: '系统设置',
    icon: Settings,
    href: '/admin/settings',
    description: '系统参数和配置'
  },
];

interface AdminSidebarProps {
  className?: string;
}

export function AdminSidebar({ className }: AdminSidebarProps) {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === '/admin') {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <Sidebar className={cn("border-r", className)}>
      <SidebarHeader className="border-b px-6 py-4">
        <div className="flex items-center gap-2">
          <div className="h-8 w-8 rounded-lg flex items-center justify-center">
            <img src="/logo-w.png" alt="logo" />
          </div>
          <div>
            <h2 className="text-lg font-semibold">多业务管理</h2>
            <p className="text-xs text-muted-foreground">统一后台管理系统</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-3 py-4">
        <SidebarMenu>
          {menuItems.map((item) => (
            <SidebarMenuItem key={item.href}>
              {item.children ? (
                <div className="space-y-1">
                  <SidebarMenuButton
                    asChild
                    isActive={isActive(item.href)}
                    className="w-full justify-start"
                  >
                    <Link href={item.href} className="flex items-center gap-3">
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                  {isActive(item.href) && (
                    <div className="ml-6 space-y-1">
                      {item.children.map((child) => (
                        <SidebarMenuButton
                          key={child.href}
                          asChild
                          isActive={pathname === child.href}
                          size="sm"
                        >
                          <Link href={child.href}>
                            {child.title}
                          </Link>
                        </SidebarMenuButton>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <SidebarMenuButton
                  asChild
                  isActive={isActive(item.href)}
                  className="w-full justify-start"
                  tooltip={item.description}
                >
                  <Link href={item.href} className="flex items-center gap-3">
                    <item.icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </Link>
                </SidebarMenuButton>
              )}
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="border-t p-4">
        <Button 
          variant="ghost" 
          className="w-full justify-start gap-3"
          onClick={() => {
            // 这里添加登出逻辑
            console.log('退出登录');
          }}
        >
          <LogOut className="h-4 w-4" />
          退出登录
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
} 