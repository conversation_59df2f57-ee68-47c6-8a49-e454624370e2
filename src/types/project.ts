export interface Project {
  id: number;
  name: string;
  description: string;
  domain: string;
  logo?: string;
  dbHost: string;
  dbPort: number;
  dbName: string;
  dbUser: string;
  isActive: boolean;
  config: {
    features: string[];
    s3?: {
      bucket: string;
      cdn: string;
    };
  };
  createdAt: string;
}

export interface ProjectConfig {
  id: number;
  name: string;
  dbHost: string;
  dbPort: number;
  dbName: string;
  dbUser: string;
  dbPassword: string;
}

export interface ProjectConnection {
  projectId: number;
  connectionString: string;
} 