import { faqCategoryTable, faqTable } from "@/lib/db";

export type FaqType = typeof faqTable.$inferSelect;

export type FaqCategoryType = typeof faqCategoryTable.$inferSelect;

export interface FaqFormData {
  question: string;
  answer: string; // This will store JSON string from Tiptap editor
  categoryId?: number;
  status: string; // draft, published, archived
  sort: number; // Integer for ordering FAQs
}

// FAQ status options
export const FAQ_STATUS_OPTIONS = [
  { value: 'draft', label: '草稿' },
  { value: 'published', label: '已发布' },
  { value: 'archived', label: '已归档' }
] as const;

export type FaqStatus = typeof FAQ_STATUS_OPTIONS[number]['value'];

// Helper type for working with parsed Tiptap content
export interface FaqRichTextContent {
  type: string;
  content?: FaqRichTextContent[];
  text?: string;
  marks?: Array<{
    type: string;
    attrs?: Record<string, unknown>;
  }>;
  attrs?: Record<string, unknown>;
}
