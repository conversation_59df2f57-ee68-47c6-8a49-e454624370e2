export interface BlogCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  sort: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface BlogPost {
  id?: number;
  title: string;
  slug: string;
  summary?: string;
  content: string;
  categoryId: string;
  tags: string[];
  status: string;
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string;
  authorName: string;
  authorEmail: string;
  thumbnail?: string;
  isTop?: boolean;
  viewCount?: number;
  likeCount?: number;
  publishedAt?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface BlogPostWithCategory extends BlogPost {
  id: number;
  categoryName?: string;
  categorySlug?: string;
}

export interface BlogListResponse {
  posts: BlogPostWithCategory[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface BlogFormData {
  title: string;
  slug: string;
  summary?: string;
  content: string;
  categoryId: string;
  tags: string[];
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string;
  authorName: string;
  authorEmail: string;
} 