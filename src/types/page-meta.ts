// 页面元数据相关类型定义

// JSON-LD 结构化数据类型  
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface JsonLdData extends Record<string, any> {
  '@context': string;
  '@type': string;
}

// OpenGraph 数据类型
export interface OpenGraphData {
  // 基础OpenGraph标签
  'og:title'?: string;
  'og:description'?: string;
  'og:image'?: string;
  'og:url'?: string;
  'og:type'?: string;
  'og:site_name'?: string;
  'og:locale'?: string;
  
  // 文章类型
  'article:author'?: string;
  'article:published_time'?: string;
  'article:modified_time'?: string;
  'article:section'?: string;
  'article:tag'?: string[];
  
  // 视频类型
  'og:video'?: string;
  'og:video:width'?: string;
  'og:video:height'?: string;
  'og:video:type'?: string;
  
  // 音频类型
  'og:audio'?: string;
  'og:audio:type'?: string;
  
  // 产品类型
  'product:price:amount'?: string;
  'product:price:currency'?: string;
  'product:availability'?: string;
  
  // 推特卡片
  'twitter:card'?: string;
  'twitter:title'?: string;
  'twitter:description'?: string;
  'twitter:image'?: string;
  'twitter:site'?: string;
  'twitter:creator'?: string;
  'twitter:player'?: string;
  'twitter:player:width'?: string;
  'twitter:player:height'?: string;
  
  // 其他平台
  'fb:app_id'?: string;
  'fb:admins'?: string;
  
  [key: string]: string | string[] | undefined;
}

// 页面元数据项
export interface PageMetaItem {
  id: number;
  path: string;
  title?: string;
  description?: string;
  keywords?: string;
  language: string;
  isActive: boolean;
  canonical?: string;
  noIndex: boolean;
  jsonLd?: JsonLdData;
  openGraph?: OpenGraphData;
  createdAt: string;
  updatedAt: string;
}

// 页面元数据列表响应
export interface PageMetaList {
  metaList: PageMetaItem[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// 表单数据类型
export interface PageMetaFormData {
  path: string;
  title: string;
  description: string;
  keywords: string;
  language: string;
  isActive: boolean;
  canonical: string;
  noIndex: boolean;
  jsonLd: JsonLdData | null;
  openGraph: OpenGraphData | null;
} 