import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { mainDb, users } from "@/lib/db";
import { eq } from "drizzle-orm";
import { verifyPassword } from "@/lib/crypto";

export const { handlers, auth, signIn, signOut } = NextAuth({
  trustHost: true,
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "邮箱", type: "email" },
        password: { label: "密码", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // 默认管理员用户，直接允许登录
           // 从环境变量获取默认密码，如果没有设置则使用默认值
            const defaultPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123';
            
            if (credentials.password === defaultPassword) {
              return {
                id: 'admin-default',
                email: credentials.email as string,
                name: '默认管理员',
                image: null,
              };
            } 
          // 其他用户需要查询数据库
          const user = await mainDb
            .select()
            .from(users)
            .where(eq(users.email, credentials.email as string))
            .limit(1);

          if (!user.length || !user[0].password) {
            return null;
          }

          // 分离存储的哈希值和盐值
          const passwordParts = user[0].password.split(':');
          if (passwordParts.length !== 2) {
            return null;
          }
          
          const [hash, salt] = passwordParts;
          if (!hash || !salt) {
            return null;
          }

          // 使用异步密码验证
          const isValidPassword = await verifyPassword(
            credentials.password as string,
            hash,
            salt
          );

          if (!isValidPassword) {
            return null;
          }

          return {
            id: user[0].id,
            email: user[0].email,
            name: user[0].name,
            image: user[0].avatar,
          };
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60 * 7, // 24 小时
  },
  jwt: {
    maxAge: 24 * 60 * 60 * 7, // 24 小时
  },
  callbacks: {
    async jwt({ token, user }) {
      // 用户首次登录时，将用户信息添加到 token
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.image = user.image;
      }
      return token;
    },
    async session({ session, token }) {
      // 将 token 中的信息传递到 session
      if (token) {
        session.user.id = token.id as string;
        session.user.email = token.email as string;
        session.user.name = token.name as string;
        session.user.image = token.image as string;
      }
      return session;
    },
  },
}); 