/**
 * 边缘运行时兼容的加密工具函数
 * 使用 Web Crypto API 替代 Node.js crypto 模块
 */

const ALGORITHM = 'AES-GCM';

/**
 * 获取加密密钥
 */
async function getEncryptionKey(): Promise<CryptoKey> {
  const keyString = process.env.ENCRYPTION_KEY || 'default-32-char-secret-key-here!!';
  
  const encoder = new TextEncoder();
  const processedKey = keyString.slice(0, 32).padEnd(32, '0');
  
  const keyData = encoder.encode(processedKey);
  
  return await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: ALGORITHM },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * 加密文本
 * @param text 要加密的文本
 * @returns 加密后的文本 (base64编码的iv:ciphertext)
 */
export async function encrypt(text: string): Promise<string> {
  try {
    console.log('[ENCRYPT] 开始加密，输入长度:', text.length);
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    const key = await getEncryptionKey();
    
    const iv = crypto.getRandomValues(new Uint8Array(12)); // GCM uses 12-byte IV
    console.log('[ENCRYPT] IV长度:', iv.length);
    
    const encrypted = await crypto.subtle.encrypt(
      { name: ALGORITHM, iv },
      key,
      data
    );
    
    console.log('[ENCRYPT] 加密数据长度:', encrypted.byteLength);
    
    // 将 iv 和加密数据组合并转为 base64
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);
    
    // 使用更安全的base64编码方式
    let binary = '';
    for (let i = 0; i < combined.length; i++) {
      binary += String.fromCharCode(combined[i]);
    }
    const result = btoa(binary);
    
    console.log('[ENCRYPT] 最终结果长度:', result.length);
    return result;
  } catch (error) {
    console.error('[ENCRYPT] 加密失败:', error);
    throw error;
  }
}

/**
 * 解密文本
 * @param encryptedText base64编码的加密文本
 * @returns 解密后的文本
 */
export async function decrypt(encryptedText: string): Promise<string> {
  try {
    
    // 解码 base64
    const binaryString = atob(encryptedText);
    
    const combined = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      combined[i] = binaryString.charCodeAt(i);
    }
        
    if (combined.length < 12) {
      throw new Error('加密数据太短，无法提取IV');
    }
    
    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);
    
    const key = await getEncryptionKey();
    const decrypted = await crypto.subtle.decrypt(
      { name: ALGORITHM, iv },
      key,
      encrypted
    );
    
    const decoder = new TextDecoder();
    const result = decoder.decode(decrypted);
    return result;
  } catch (error) {
    console.error('[DECRYPT] 解密失败:', error);
    throw new Error(`解密失败：${error instanceof Error ? error.message : '无效的加密格式或密钥'}`);
  }
}

/**
 * 生成随机字符串
 * @param length 字符串长度
 * @returns 随机字符串
 */
export function generateRandomKey(length: number = 32): string {
  const array = crypto.getRandomValues(new Uint8Array(length));
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * 哈希密码 (使用 PBKDF2)
 * @param password 原始密码
 * @param salt 盐值（可选）
 * @returns 哈希后的密码和盐值
 */
export async function hashPassword(password: string, salt?: string): Promise<{ hash: string; salt: string }> {
  const passwordSalt = salt || generateRandomKey(16);
  const encoder = new TextEncoder();
  
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    encoder.encode(password),
    'PBKDF2',
    false,
    ['deriveBits']
  );
  
  const hashBuffer = await crypto.subtle.deriveBits(
    {
      name: 'PBKDF2',
      salt: encoder.encode(passwordSalt),
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    512 // 64 bytes = 512 bits
  );
  
  const hashArray = new Uint8Array(hashBuffer);
  const hash = Array.from(hashArray, byte => byte.toString(16).padStart(2, '0')).join('');
  
  return {
    hash,
    salt: passwordSalt
  };
}

/**
 * 验证密码
 * @param password 原始密码
 * @param hash 存储的哈希值
 * @param salt 盐值
 * @returns 是否匹配
 */
export async function verifyPassword(password: string, hash: string, salt: string): Promise<boolean> {
  const { hash: newHash } = await hashPassword(password, salt);
  return hash === newHash;
}

/**
 * 生成 JWT 密钥
 */
export async function generateJWTKey(): Promise<CryptoKey> {
  const secret = process.env.NEXTAUTH_SECRET || 'your-secret-key';
  const encoder = new TextEncoder();
  return await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign', 'verify']
  );
}

/**
 * 创建 JWT Token
 */
export async function createJWT(payload: Record<string, unknown>, expiresIn: number = 3600): Promise<string> {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
  
  const now = Math.floor(Date.now() / 1000);
  const jwtPayload = {
    ...payload,
    iat: now,
    exp: now + expiresIn
  };
  
  const encoder = new TextEncoder();
  const headerBase64 = btoa(JSON.stringify(header)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
  const payloadBase64 = btoa(JSON.stringify(jwtPayload)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
  
  const data = encoder.encode(`${headerBase64}.${payloadBase64}`);
  const key = await generateJWTKey();
  const signature = await crypto.subtle.sign('HMAC', key, data);
  
  const signatureBase64 = btoa(String.fromCharCode(...new Uint8Array(signature)))
    .replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_');
  
  return `${headerBase64}.${payloadBase64}.${signatureBase64}`;
}

/**
 * 验证 JWT Token
 */
export async function verifyJWT(token: string): Promise<Record<string, unknown>> {
  try {
    const [headerBase64, payloadBase64, signatureBase64] = token.split('.');
    if (!headerBase64 || !payloadBase64 || !signatureBase64) {
      throw new Error('Invalid token format');
    }
    
    const encoder = new TextEncoder();
    const data = encoder.encode(`${headerBase64}.${payloadBase64}`);
    const key = await generateJWTKey();
    
    // 恢复 base64 padding 并转换签名
    const signatureWithPadding = signatureBase64.replace(/-/g, '+').replace(/_/g, '/').padEnd(
      signatureBase64.length + (4 - signatureBase64.length % 4) % 4, '='
    );
    const signature = new Uint8Array(atob(signatureWithPadding).split('').map(c => c.charCodeAt(0)));
    
    const isValid = await crypto.subtle.verify('HMAC', key, signature, data);
    if (!isValid) {
      throw new Error('Invalid signature');
    }
    
    // 解码 payload
    const payloadWithPadding = payloadBase64.replace(/-/g, '+').replace(/_/g, '/').padEnd(
      payloadBase64.length + (4 - payloadBase64.length % 4) % 4, '='
    );
    const payload = JSON.parse(atob(payloadWithPadding));
    
    // 检查过期时间
    if (payload.exp && Date.now() / 1000 > payload.exp) {
      throw new Error('Token expired');
    }
    
    return payload;
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`JWT verification failed: ${message}`);
  }
} 