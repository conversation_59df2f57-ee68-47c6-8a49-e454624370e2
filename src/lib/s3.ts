import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

// 配置 AWS S3
const s3 = new S3Client({
  region: process.env.AWS_REGION || 'us-west-2',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

export interface UploadOptions {
  bucket?: string;
  folder?: string;
  fileName?: string;
  contentType?: string;
}

/**
 * 上传文件到 S3
 */
export async function uploadToS3(
  file: File | Buffer,
  options: UploadOptions = {}
): Promise<{ url: string; key: string }> {
  const {
    bucket = process.env.AWS_S3_BUCKET_NAME!,
    folder = 'uploads',
    fileName,
    contentType,
  } = options;

  if (!bucket) {
    throw new Error('S3 bucket name is required');
  }

  // 生成文件名
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const ext = file instanceof File ? file.name.split('.').pop() : 'jpg';
  const key = `${folder}/${fileName || `${timestamp}-${randomString}.${ext}`}`;

  // 处理文件内容
  let body: Buffer | Uint8Array;
  if (file instanceof File) {
    body = Buffer.from(await file.arrayBuffer());
  } else {
    body = file;
  }

  // 上传
  const putCommand = new PutObjectCommand({
    Bucket: bucket,
    Key: key,
    Body: body,
    ContentType: contentType || (file instanceof File ? file.type : 'image/jpeg'),
  });
  await s3.send(putCommand);

  // 拼接公开URL
  const region = process.env.AWS_REGION || 'us-west-2';
  const url = `https://${bucket}.s3.${region}.amazonaws.com/${key}`;
  return {
    url,
    key,
  };
}
