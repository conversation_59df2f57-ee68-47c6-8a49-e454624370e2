// 业务数据库预期表结构DDL
export const businessTableDDL = {
  // TDK页面SEO配置表
  page_meta: `
    CREATE TABLE page_meta (
      id SERIAL PRIMARY KEY,
      path VARCHAR(500) UNIQUE NOT NULL,
      title VARCHAR(255),
      description TEXT,
      keywords VARCHAR(500),
      language VARCHAR(10) DEFAULT 'en',
      is_active BOOLEAN DEFAULT true,
      json_ld JSONB,
      open_graph JSONB,
      canonical VARCHAR(500),
      no_index BOOLEAN DEFAULT false,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // 博客分类表
  blog_categories: `
    CREATE TABLE blog_categories (
      id SERIAL PRIMARY KEY,
      name VARCHAR(100) NOT NULL,
      slug VARCHAR(100) UNIQUE NOT NULL,
      description TEXT,
      sort INTEGER DEFAULT 0,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // 博客标签表
  blog_tags: `
    CREATE TABLE blog_tags (
      id SERIAL PRIMARY KEY,
      name VARCHAR(50) UNIQUE NOT NULL,
      color VARCHAR(7) DEFAULT '#3b82f6',
      count INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // 博客文章表
  blog_posts: `
    CREATE TABLE blog_posts (
      id SERIAL PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      slug VARCHAR(255) UNIQUE NOT NULL,
      summary TEXT,
      content TEXT,
      thumbnail TEXT,
      category_id INTEGER REFERENCES blog_categories(id),
      status VARCHAR(20) DEFAULT 'draft',
      is_top BOOLEAN DEFAULT false,
      view_count INTEGER DEFAULT 0,
      like_count INTEGER DEFAULT 0,
      published_at TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      author_name VARCHAR(100),
      author_email VARCHAR(255),
      meta_title VARCHAR(255),
      meta_description TEXT,
      meta_keywords VARCHAR(500)
    );
  `,

  // 博客文章标签关联表
  blog_post_tags: `
    CREATE TABLE blog_post_tags (
      id SERIAL PRIMARY KEY,
      post_id INTEGER REFERENCES blog_posts(id) ON DELETE CASCADE,
      tag_id INTEGER REFERENCES blog_tags(id) ON DELETE CASCADE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // 站点配置表
  site_config: `
    CREATE TABLE site_config (
      id SERIAL PRIMARY KEY,
      key VARCHAR(255) UNIQUE NOT NULL,
      value TEXT,
      type VARCHAR(50) DEFAULT 'string',
      category VARCHAR(100) DEFAULT 'general',
      description TEXT,
      is_public BOOLEAN DEFAULT false,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,

  // 页面访问统计表
  page_stats: `
    CREATE TABLE page_stats (
      id SERIAL PRIMARY KEY,
      path VARCHAR(500) NOT NULL,
      ip VARCHAR(45),
      user_agent TEXT,
      referer TEXT,
      country VARCHAR(100),
      city VARCHAR(100),
      device VARCHAR(50),
      browser VARCHAR(50),
      os VARCHAR(50),
      visited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `,
  faq_categories: `create table faq_categories
(
    id          serial
        primary key,
    name        varchar(100) not null,
    slug        varchar(100) not null
        constraint faq_categories_slug_unique
            unique,
    description text,
    sort        integer   default 0,
    is_active   boolean   default true,
    created_at  timestamp default now(),
    updated_at  timestamp default now()
);
     `,
  faq: `create table faq
(
    id          serial
        primary key,
    question    varchar(255) not null,
    answer      text,
    category_id integer
        constraint faq_category_id_faq_categories_id_fk
            references faq_categories,
    created_at  timestamp default now(),
    updated_at  timestamp default now(),
    sort        integer   default 0,
    status      varchar   default 'draft'::character varying
);`,
};

// 表结构描述信息
export const businessTableDescriptions = {
  page_meta: {
    name: "页面Meta配置",
    description:
      "用于管理网站页面的SEO信息，包括标题、描述、关键词、结构化数据等",
    features: ["page_meta"],
  },
  blog_categories: {
    name: "博客分类",
    description: "博客文章的分类管理",
    features: ["blog"],
  },
  blog_tags: {
    name: "博客标签",
    description: "博客文章的标签管理",
    features: ["blog"],
  },
  blog_posts: {
    name: "博客文章",
    description: "博客文章内容管理",
    features: ["blog"],
  },
  blog_post_tags: {
    name: "博客文章标签关联",
    description: "博客文章与标签的多对多关联",
    features: ["blog"],
  },
  site_config: {
    name: "站点配置",
    description: "网站全局配置参数管理",
    features: ["page_meta"],
  },
  page_stats: {
    name: "页面访问统计",
    description: "网站页面访问数据统计",
    features: ["page_analytics"],
  },
  faq_categories: {
    name: "FAQ分类",
    description: "对FAQ进行分类",
    features: ["faq"],
  },
  faq: {
    name: "FAQ",
    description: "FAQ内容管理",
    features: ["faq"],
  },
};

// 根据项目启用的功能模块获取需要的表
export function getRequiredTablesForFeatures(): string[] {
  const requiredTables: string[] = [];
  Object.entries(businessTableDescriptions).forEach(([tableName]) => {
    requiredTables.push(tableName);
  });

  return requiredTables;
}

// 获取表的创建SQL
export function getTableDDL(tableName: string): string | null {
  return businessTableDDL[tableName as keyof typeof businessTableDDL] || null;
}
