import { pgTable, serial, varchar, text, timestamp, boolean, jsonb, integer, uuid } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// 用户表
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).unique().notNull(),
  name: varchar('name', { length: 255 }),
  avatar: text('avatar'),
  password: varchar('password', { length: 255 }),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// 角色表
export const roles = pgTable('roles', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).unique().notNull(),
  description: text('description'),
  permissions: jsonb('permissions').$type<string[]>(),
  isSystem: boolean('is_system').default(false), // 系统内置角色
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// 用户角色关联表
export const userRoles = pgTable('user_roles', {
  id: serial('id').primaryKey(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  roleId: integer('role_id').references(() => roles.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow(),
});

// 菜单表
export const menus = pgTable('menus', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  path: varchar('path', { length: 255 }),
  icon: varchar('icon', { length: 100 }),
  parentId: integer('parent_id'),
  sort: integer('sort').default(0),
  isVisible: boolean('is_visible').default(true),
  permissions: jsonb('permissions').$type<string[]>(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// 业务项目表 - 用于管理多个业务数据库
export const businessProjects = pgTable('business_projects', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  logo: varchar('logo', { length: 255 }),
  description: text('description'),
  domain: varchar('domain', { length: 255 }), // 项目域名
  // 数据库连接信息（加密存储）
  dbHost: varchar('db_host', { length: 255 }).notNull(),
  dbPort: integer('db_port').default(5432),
  dbName: varchar('db_name', { length: 255 }).notNull(),
  dbUser: varchar('db_user', { length: 255 }).notNull(),
  dbPassword: text('db_password').notNull(), // 加密存储
  // 项目配置
  isActive: boolean('is_active').default(true),
  config: jsonb('config').$type<{
    s3: {
      bucket: string;
      cdn: string;
    }
  }>(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  createdBy: uuid('created_by').references(() => users.id),
});

// 系统配置表
export const systemConfig = pgTable('system_config', {
  id: serial('id').primaryKey(),
  key: varchar('key', { length: 255 }).unique().notNull(),
  value: jsonb('value'),
  description: text('description'),
  updatedAt: timestamp('updated_at').defaultNow(),
  updatedBy: uuid('updated_by').references(() => users.id),
});

// 关系定义
export const usersRelations = relations(users, ({ many }) => ({
  userRoles: many(userRoles),
  businessProjects: many(businessProjects),
}));

export const rolesRelations = relations(roles, ({ many }) => ({
  userRoles: many(userRoles),
}));

export const userRolesRelations = relations(userRoles, ({ one }) => ({
  user: one(users, {
    fields: [userRoles.userId],
    references: [users.id],
  }),
  role: one(roles, {
    fields: [userRoles.roleId],
    references: [roles.id],
  }),
}));

export const menusRelations = relations(menus, ({ one, many }) => ({
  parent: one(menus, {
    fields: [menus.parentId],
    references: [menus.id],
  }),
  children: many(menus),
}));

export const businessProjectsRelations = relations(businessProjects, ({ one }) => ({
  creator: one(users, {
    fields: [businessProjects.createdBy],
    references: [users.id],
  }),
})); 