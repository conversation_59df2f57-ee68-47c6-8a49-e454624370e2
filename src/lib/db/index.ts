import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as mainSchema from './schema/main';

// 主数据库连接
const mainConnection = postgres(process.env.MAIN_DATABASE_URL!);
export const mainDb = drizzle(mainConnection, { schema: mainSchema });

/**
 * 测试数据库连接
 * @param dbConfig 数据库配置
 */
export async function testDatabaseConnection(dbConfig: {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}): Promise<{ success: boolean; error?: string }> {
  try {
    const connectionString = `postgresql://${dbConfig.username}:${dbConfig.password}@${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`;
    const testConnection = postgres(connectionString, { max: 1 });
    
    // 执行简单查询测试连接
    await testConnection`SELECT 1`;
    await testConnection.end();
    
    return { success: true };
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : '连接测试失败' 
    };
  }
}

// 导出schema用于类型推断
export type MainDatabase = typeof mainDb;

// 导出表结构
export * from './schema/main';
export * from './schema/business'; 