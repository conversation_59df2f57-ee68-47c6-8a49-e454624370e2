import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { decrypt } from '@/lib/crypto';
import { ProjectConfig } from '@/types/project';
import { mainDb } from '@/lib/db';
import { businessProjects } from '@/lib/db/schema/main';
import * as businessSchema from '@/lib/db/schema/business';
import { eq } from 'drizzle-orm';

// 连接池管理
const connectionPool = new Map<string, postgres.Sql>();
const drizzleInstancePool = new Map<string, ReturnType<typeof drizzle>>();

/**
 * 获取项目配置
 */
export async function getProjectConfig(projectId: string | number): Promise<ProjectConfig> {
  const id = typeof projectId === 'string' ? parseInt(projectId) : projectId;
  
  const project = await mainDb.select().from(businessProjects).where(eq(businessProjects.id, id)).limit(1);
  
  if (!project || project.length === 0) {
    throw new Error('项目不存在');
  }

  const projectData = project[0];
  
  return {
    id: projectData.id,
    name: projectData.name,
    dbHost: projectData.dbHost,
    dbPort: projectData.dbPort || 5432,
    dbName: projectData.dbName,
    dbUser: projectData.dbUser,
    dbPassword: projectData.dbPassword,
  };
}

/**
 * 创建数据库连接
 */
export async function createDatabaseConnection(project: ProjectConfig): Promise<postgres.Sql> {
  const connectionKey = `${project.id}_${project.dbHost}_${project.dbPort}_${project.dbName}`;
  
  // 检查连接池中是否已有连接
  if (connectionPool.has(connectionKey)) {
    const connection = connectionPool.get(connectionKey)!;
    // 检查连接是否还有效
    try {
      await connection`SELECT 1`;
      return connection;
    } catch (error) {
      // 连接无效，从池中移除
      console.log(`Connection test failed, removing from pool: ${connectionKey}`, error);
      connectionPool.delete(connectionKey);
      drizzleInstancePool.delete(connectionKey);
    }
  }

  // 解密数据库密码
  const decryptedPassword = await decrypt(project.dbPassword);
  
  // 创建新连接
  const connectionString = `postgres://${project.dbUser}:${decryptedPassword}@${project.dbHost}:${project.dbPort}/${project.dbName}`;
  const queryClient = postgres(connectionString, {
    max: 10, // 最大连接数
    idle_timeout: 20, // 空闲超时时间（秒）
    connect_timeout: 10, // 连接超时时间（秒）
  });

  // 添加到连接池
  connectionPool.set(connectionKey, queryClient);
  
  return queryClient;
}

/**
 * 获取数据库连接和 Drizzle 实例
 */
export async function getDatabaseConnection(projectId: string | number) {
  const project = await getProjectConfig(projectId);
  const queryClient = await createDatabaseConnection(project);
  const connectionKey = `${project.id}_${project.dbHost}_${project.dbPort}_${project.dbName}`;
  
  // 检查是否已有 Drizzle 实例
  if (!drizzleInstancePool.has(connectionKey)) {
    const db = drizzle(queryClient, { schema: businessSchema });
    drizzleInstancePool.set(connectionKey, db);
  }
  
  const db = drizzleInstancePool.get(connectionKey)!;
  
  return {
    project,
    queryClient,
    db,
    // 提供一个关闭连接的方法（可选，通常让连接池管理）
    close: () => queryClient.end()
  };
}

/**
 * 获取业务数据库实例（统一接口，替代原来的getBusinessDb）
 */
export async function getBusinessDatabase(projectId: string | number) {
  const { db } = await getDatabaseConnection(projectId);
  return db;
}

/**
 * 执行数据库查询的通用方法
 */
export async function executeQuery<T = unknown>(
  projectId: string | number, 
  query: (db: ReturnType<typeof drizzle>, queryClient: postgres.Sql) => Promise<T>
): Promise<T> {
  const { db, queryClient } = await getDatabaseConnection(projectId);
  
  try {
    return await query(db, queryClient);
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
  // 注意：不在这里关闭连接，让连接池管理
}

/**
 * 关闭特定项目的数据库连接
 */
export async function closeProjectConnections(projectId: string | number) {
  const id = typeof projectId === 'string' ? parseInt(projectId) : projectId;
  const keysToRemove = Array.from(connectionPool.keys()).filter(key => 
    key.startsWith(`${id}_`)
  );
  
  const promises = keysToRemove.map(async (key) => {
    const connection = connectionPool.get(key);
    if (connection) {
      try {
        await connection.end();
      } catch (error) {
        console.error(`Error closing connection ${key}:`, error);
      }
      connectionPool.delete(key);
      drizzleInstancePool.delete(key);
    }
  });
  
  await Promise.allSettled(promises);
}

/**
 * 清理连接池中的无效连接
 */
export async function cleanupConnections() {
  const promises = Array.from(connectionPool.entries()).map(async ([key, connection]) => {
    try {
      await connection`SELECT 1`;
    } catch (error) {
      console.log(`Removing invalid connection: ${key}`, error);
      connectionPool.delete(key);
      drizzleInstancePool.delete(key);
      try {
        await connection.end();
      } catch (closeError) {
        console.error(`Error closing connection ${key}:`, closeError);
      }
    }
  });
  
  await Promise.allSettled(promises);
}

/**
 * 关闭所有连接
 */
export async function closeAllConnections() {
  const promises = Array.from(connectionPool.values()).map(connection => 
    connection.end().catch(error => console.error('Error closing connection:', error))
  );
  
  await Promise.allSettled(promises);
  connectionPool.clear();
  drizzleInstancePool.clear();
}

/**
 * 获取连接池状态
 */
export function getConnectionPoolStatus() {
  return {
    activeConnections: connectionPool.size,
    drizzleInstances: drizzleInstancePool.size,
    connectionKeys: Array.from(connectionPool.keys()),
  };
}

// 进程退出时清理连接
if (typeof process !== 'undefined') {
  process.on('beforeExit', closeAllConnections);
  process.on('SIGINT', closeAllConnections);
  process.on('SIGTERM', closeAllConnections);
} 