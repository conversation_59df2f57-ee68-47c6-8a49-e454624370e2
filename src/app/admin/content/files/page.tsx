"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Upload,
  Search,
  Grid3X3,
  List,
  Image as ImageIcon,
  File,
  Download,
  Trash2,
  Copy,
  Eye
} from 'lucide-react';

// 模拟文件数据
const files = [
  {
    id: 1,
    name: 'hero-banner.jpg',
    url: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=800',
    type: 'image/jpeg',
    size: '2.4 MB',
    dimensions: '1920x1080',
    uploadedAt: '2024-01-15',
    alt: '科技背景图片',
  },
  {
    id: 2,
    name: 'product-screenshot.png',
    url: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800',
    type: 'image/png',
    size: '1.8 MB',
    dimensions: '1600x900',
    uploadedAt: '2024-01-14',
    alt: '产品截图',
  },
  {
    id: 3,
    name: 'team-photo.jpg',
    url: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800',
    type: 'image/jpeg',
    size: '3.2 MB',
    dimensions: '2048x1536',
    uploadedAt: '2024-01-13',
    alt: '团队合影',
  },
  {
    id: 4,
    name: 'logo-variants.svg',
    url: '#',
    type: 'image/svg+xml',
    size: '245 KB',
    dimensions: '矢量图',
    uploadedAt: '2024-01-12',
    alt: '公司Logo各种变体',
  },
  {
    id: 5,
    name: 'presentation.pdf',
    url: '#',
    type: 'application/pdf',
    size: '8.5 MB',
    dimensions: 'PDF文档',
    uploadedAt: '2024-01-11',
    alt: '产品介绍演示文稿',
  },
  {
    id: 6,
    name: 'tutorial-video.mp4',
    url: '#',
    type: 'video/mp4',
    size: '45.2 MB',
    dimensions: '1920x1080',
    uploadedAt: '2024-01-10',
    alt: '产品使用教程视频',
  },
];

const getFileIcon = (type: string) => {
  if (type.startsWith('image/')) {
    return <ImageIcon className="h-8 w-8 text-blue-500" />;
  }
  return <File className="h-8 w-8 text-gray-500" />;
};

const formatFileSize = (size: string) => {
  return size;
};

const isImage = (type: string) => type.startsWith('image/');

export default function FilesManagementPage() {
  const [viewMode, setViewMode] = React.useState<'grid' | 'list'>('grid');

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">文件管理</h1>
          <p className="text-muted-foreground">
            管理上传的图片、文档和媒体文件
          </p>
        </div>
        <div className="flex gap-2">
          <Button>
            <Upload className="mr-2 h-4 w-4" />
            上传文件
          </Button>
        </div>
      </div>

      {/* 筛选和搜索 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="搜索文件名..." className="pl-8" />
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 文件列表 */}
      <Card>
        <CardHeader>
          <CardTitle>文件库</CardTitle>
          <CardDescription>
            共 {files.length} 个文件，总大小约 62.4 MB
          </CardDescription>
        </CardHeader>
        <CardContent>
          {viewMode === 'grid' ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {files.map((file) => (
                <div key={file.id} className="group relative border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                  {/* 文件预览 */}
                  <div className="aspect-video bg-muted flex items-center justify-center relative">
                    {isImage(file.type) && file.url !== '#' ? (
                      <img
                        src={file.url}
                        alt={file.alt}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex flex-col items-center justify-center">
                        {getFileIcon(file.type)}
                        <span className="text-xs text-muted-foreground mt-2">
                          {file.type.split('/')[1]?.toUpperCase()}
                        </span>
                      </div>
                    )}
                    
                    {/* 悬停操作 */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                      <Button size="sm" variant="secondary">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="secondary">
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="secondary">
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="destructive">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* 文件信息 */}
                  <div className="p-3 space-y-2">
                    <div className="font-medium text-sm truncate" title={file.name}>
                      {file.name}
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>{formatFileSize(file.size)}</span>
                      <span>{file.uploadedAt}</span>
                    </div>
                    {file.dimensions && (
                      <div className="text-xs text-muted-foreground">
                        {file.dimensions}
                      </div>
                    )}
                    {file.alt && (
                      <div className="text-xs bg-muted px-2 py-1 rounded">
                        Alt: {file.alt}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {files.map((file) => (
                <div key={file.id} className="flex items-center gap-4 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex-shrink-0">
                    {isImage(file.type) && file.url !== '#' ? (
                      <img
                        src={file.url}
                        alt={file.alt}
                        className="w-12 h-12 object-cover rounded"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-muted rounded flex items-center justify-center">
                        {getFileIcon(file.type)}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{file.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {file.alt || '暂无描述'}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{file.dimensions}</span>
                    <span>{formatFileSize(file.size)}</span>
                    <span>{file.uploadedAt}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="ghost">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Copy className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 上传统计 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">总文件数</p>
                <p className="text-2xl font-bold">{files.length}</p>
              </div>
              <File className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">图片文件</p>
                <p className="text-2xl font-bold">
                  {files.filter(f => isImage(f.type)).length}
                </p>
              </div>
              <ImageIcon className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium">存储使用</p>
                <p className="text-2xl font-bold">62.4 MB</p>
              </div>
              <div className="text-muted-foreground">
                <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                  45%
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 