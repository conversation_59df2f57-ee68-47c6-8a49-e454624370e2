"use client";

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Plus,
  Edit,
  Trash2,
  Loader2,
  Database,
  Tags,
  ArrowUpDown
} from 'lucide-react';
import { Project } from '@/types/project';
import { BlogCategory } from '@/types/blog';

export default function CategoriesManagementPage() {
  const searchParams = useSearchParams();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<BlogCategory | null>(null);
  const [saving, setSaving] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    sort: 0,
  });

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
    loadCategories(projectId);
  };

  const loadCategories = async (projectId: number) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/content/categories?projectId=${projectId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data || []);
        } else {
          setError(result.error || '获取分类失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('加载分类数据时出错');
      console.error('Load categories error:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初始化项目ID
  useEffect(() => {
    const projectIdParam = searchParams.get('projectId');
    if (projectIdParam && !selectedProject) {
      // 项目选择器会自动处理
    }
  }, [searchParams]);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // 如果是分类名称且slug为空，自动生成slug
    if (field === 'name' && !formData.slug) {
      setFormData(prev => ({
        ...prev,
        slug: generateSlug(value as string)
      }));
    }
  };

  const openAddDialog = () => {
    setEditingCategory(null);
    setFormData({
      name: '',
      slug: '',
      description: '',
      sort: categories.length,
    });
    setIsDialogOpen(true);
  };

  const openEditDialog = (category: BlogCategory) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description || '',
      sort: category.sort,
    });
    setIsDialogOpen(true);
  };

  const handleSave = async () => {
    if (!selectedProject) {
      setError('请先选择一个项目');
      return;
    }

    if (!formData.name.trim() || !formData.slug.trim()) {
      setError('请填写分类名称和别名');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const method = editingCategory ? 'PUT' : 'POST';
      const url = editingCategory 
        ? `/api/content/categories/${editingCategory.id}` 
        : '/api/content/categories';

      const body = editingCategory
        ? { id: editingCategory.id, projectId: selectedProject.id, ...formData }
        : { projectId: selectedProject.id, ...formData };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          await loadCategories(selectedProject.id);
          setIsDialogOpen(false);
          setFormData({ name: '', slug: '', description: '', sort: 0 });
        } else {
          setError(result.error || '保存分类失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('保存分类时出错');
      console.error('Save category error:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (categoryId: number) => {
    if (!selectedProject) return;
    
    if (!confirm('确定要删除这个分类吗？')) return;

    try {
      const response = await fetch(`/api/content/categories/${categoryId}?projectId=${selectedProject.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          await loadCategories(selectedProject.id);
        } else {
          setError(result.error || '删除分类失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('删除分类时出错');
      console.error('Delete category error:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="分类管理"
        description="管理博客文章分类和组织结构"
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
        actions={
          selectedProject && (
            <Button onClick={openAddDialog}>
              <Plus className="mr-2 h-4 w-4" />
              新建分类
            </Button>
          )
        }
        error={error}
        onRetry={() => selectedProject && loadCategories(selectedProject.id)}
      />

      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来管理分类</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tags className="h-5 w-5" />
              分类列表
            </CardTitle>
            <CardDescription>
              {loading ? '加载中...' : `共 ${categories.length} 个分类`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p className="text-muted-foreground">加载分类列表中...</p>
                </div>
              </div>
            ) : categories.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <Tags className="h-12 w-12 mx-auto mb-4" />
                <p className="text-lg font-medium mb-2">暂无分类</p>
                <p>开始创建您的第一个分类吧</p>
                <Button onClick={openAddDialog} className="mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  新建分类
                </Button>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>分类信息</TableHead>
                    <TableHead>URL别名</TableHead>
                    <TableHead className="flex items-center gap-1">
                      <ArrowUpDown className="h-4 w-4" />
                      排序
                    </TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>创建时间</TableHead>
                    <TableHead className="text-right">操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {categories
                    .sort((a, b) => a.sort - b.sort)
                    .map((category) => (
                    <TableRow key={category.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{category.name}</div>
                          {category.description && (
                            <div className="text-sm text-muted-foreground">
                              {category.description}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="text-sm bg-muted px-2 py-1 rounded">
                          {category.slug}
                        </code>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{category.sort}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={category.isActive ? 'default' : 'secondary'}>
                          {category.isActive ? '启用' : '禁用'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-muted-foreground">
                          {formatDate(category.createdAt)}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center gap-1 justify-end">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => openEditDialog(category)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDelete(category.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      )}

      {/* 新建/编辑分类对话框 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? '编辑分类' : '新建分类'}
            </DialogTitle>
            <DialogDescription>
              {editingCategory ? '修改分类信息' : '创建一个新的文章分类'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">分类名称 *</Label>
                <Input
                  id="name"
                  placeholder="输入分类名称"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">URL别名 *</Label>
                <Input
                  id="slug"
                  placeholder="url-friendly-slug"
                  value={formData.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">分类描述</Label>
              <Textarea
                id="description"
                placeholder="简要描述这个分类..."
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sort">排序序号</Label>
              <Input
                id="sort"
                type="number"
                placeholder="0"
                value={formData.sort}
                onChange={(e) => handleInputChange('sort', parseInt(e.target.value) || 0)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handleSave} disabled={saving}>
              {saving ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              {editingCategory ? '保存修改' : '创建分类'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 