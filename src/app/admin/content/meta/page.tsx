"use client";

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Plus,
  Search,
  Edit,
  Trash2,
  Loader2,
  <PERSON>ting<PERSON>,
  Globe,
  Eye,
  EyeOff,
  Calendar,
  Code,
  Share2
} from 'lucide-react';
import { Project } from '@/types/project';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import JsonLdEditor from '@/components/ui/json-ld-editor';
import OpenGraphEditor from '@/components/ui/opengraph-editor';
import { PageMetaItem, PageMetaList, JsonLdData, OpenGraphData } from '@/types/page-meta';

export default function PageMetaManagementPage() {
  const searchParams = useSearchParams();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [pageMetaData, setPageMetaData] = useState<PageMetaList | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingMeta, setEditingMeta] = useState<PageMetaItem | null>(null);
  const [saving, setSaving] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });

  // 筛选状态
  const [searchTerm, setSearchTerm] = useState('');
  const [languageFilter, setLanguageFilter] = useState('全部');

  // 表单数据
  const [formData, setFormData] = useState<{
    path: string;
    title: string;
    description: string;
    keywords: string;
    language: string;
    isActive: boolean;
    canonical: string;
    noIndex: boolean;
    jsonLd: JsonLdData | null;
    openGraph: OpenGraphData | null;
  }>({
    path: '',
    title: '',
    description: '',
    keywords: '',
    language: 'zh-CN',
    isActive: true,
    canonical: '',
    noIndex: false,
    jsonLd: null,
    openGraph: null,
  });

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
    loadPageMetaData(projectId);
  };

  const loadPageMetaData = async (projectId: number, page: number = 1) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        projectId: projectId.toString(),
        page: page.toString(),
        limit: '10',
      });

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      if (languageFilter && languageFilter !== '全部') {
        params.append('language', languageFilter);
      }

      const response = await fetch(`/api/content/page-meta?${params}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setPageMetaData(result.data);
          setPagination(result.data.pagination);
        } else {
          setError(result.error || '获取页面元数据失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('加载页面元数据时出错');
      console.error('Load page meta error:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初始化项目ID
  useEffect(() => {
    const projectIdParam = searchParams.get('projectId');
    if (projectIdParam && !selectedProject) {
      // 项目选择器会自动处理
    }
  }, [searchParams]);

  const handleInputChange = (field: string, value: string | boolean | JsonLdData | OpenGraphData | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const openAddDialog = () => {
    setEditingMeta(null);
    setFormData({
      path: '',
      title: '',
      description: '',
      keywords: '',
      language: 'zh-CN',
      isActive: true,
      canonical: '',
      noIndex: false,
      jsonLd: null,
      openGraph: null,
    });
    setIsDialogOpen(true);
  };

  const openEditDialog = (meta: PageMetaItem) => {
    setEditingMeta(meta);
    
    // 解析JSON字段
    let parsedJsonLd = null;
    let parsedOpenGraph = null;
    
    try {
      if (meta.jsonLd && typeof meta.jsonLd === 'string') {
        parsedJsonLd = JSON.parse(meta.jsonLd);
      } else if (meta.jsonLd && typeof meta.jsonLd === 'object') {
        parsedJsonLd = meta.jsonLd;
      }
    } catch (error) {
      console.error('解析JSON-LD数据失败:', error);
    }
    
    try {
      if (meta.openGraph && typeof meta.openGraph === 'string') {
        parsedOpenGraph = JSON.parse(meta.openGraph);
      } else if (meta.openGraph && typeof meta.openGraph === 'object') {
        parsedOpenGraph = meta.openGraph;
      }
    } catch (error) {
      console.error('解析OpenGraph数据失败:', error);
    }
    
    setFormData({
      path: meta.path,
      title: meta.title || '',
      description: meta.description || '',
      keywords: meta.keywords || '',
      language: meta.language,
      isActive: meta.isActive,
      canonical: meta.canonical || '',
      noIndex: meta.noIndex,
      jsonLd: parsedJsonLd,
      openGraph: parsedOpenGraph,
    });
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setEditingMeta(null);
    setSaving(false);
  };

  const handleSubmit = async () => {
    if (!selectedProject || !formData.path) return;

    setSaving(true);
    try {
      const url = editingMeta
        ? `/api/content/page-meta/${editingMeta.id}`
        : '/api/content/page-meta';
      
      const method = editingMeta ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: selectedProject.id,
          ...formData,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          closeDialog();
          loadPageMetaData(selectedProject.id, pagination.page);
        } else {
          setError(result.error || '操作失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('操作失败');
      console.error('Submit error:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (meta: PageMetaItem) => {
    if (!selectedProject || !confirm('确定要删除这个页面元数据吗？此操作不可逆。')) return;

    try {
      const response = await fetch(`/api/content/page-meta/${meta.id}?projectId=${selectedProject.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          loadPageMetaData(selectedProject.id, pagination.page);
        } else {
          setError(result.error || '删除失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('删除失败');
      console.error('Delete error:', err);
    }
  };

  const handleSearch = () => {
    if (selectedProject) {
      loadPageMetaData(selectedProject.id, 1);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (selectedProject) {
      loadPageMetaData(selectedProject.id, newPage);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="default" className="bg-green-500">
        <Globe className="mr-1 h-3 w-3" />
        启用
      </Badge>
    ) : (
      <Badge variant="secondary">
        <EyeOff className="mr-1 h-3 w-3" />
        禁用
      </Badge>
    );
  };

  const getIndexBadge = (noIndex: boolean) => {
    return noIndex ? (
      <Badge variant="destructive">
        <EyeOff className="mr-1 h-3 w-3" />
        不索引
      </Badge>
    ) : (
      <Badge variant="outline">
        <Eye className="mr-1 h-3 w-3" />
        可索引
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="页面Meta配置"
        description="管理页面的SEO元数据、TDK设置和搜索引擎优化配置"
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
        actions={
          selectedProject && (
            <Button onClick={openAddDialog}>
              <Plus className="mr-2 h-4 w-4" />
              新建配置
            </Button>
          )
        }
        error={error}
        onRetry={() => selectedProject && loadPageMetaData(selectedProject.id)}
      />

      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <Settings className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来管理SEO配置</p>
            </div>
          </CardContent>
        </Card>
      ) : loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">加载SEO配置数据中...</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* 搜索和筛选 */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索页面路径或标题..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    className="pl-8"
                  />
                </div>

                <Select value={languageFilter} onValueChange={setLanguageFilter}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="语言" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="全部">全部语言</SelectItem>
                    <SelectItem value="zh-CN">中文</SelectItem>
                    <SelectItem value="en">英文</SelectItem>
                  </SelectContent>
                </Select>

                <Button onClick={handleSearch}>
                  <Search className="h-4 w-4 mr-2" />
                  搜索
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 数据表格 */}
          <Card>
            <CardHeader>
              <CardTitle>Meta配置列表</CardTitle>
              <CardDescription>
                当前项目的页面Meta配置 ({pageMetaData?.pagination.total || 0} 个)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {pageMetaData?.metaList.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Settings className="h-12 w-12 mx-auto mb-4" />
                  <p>暂无Meta配置数据</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>页面路径</TableHead>
                        <TableHead>标题</TableHead>
                        <TableHead>语言</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>索引</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {pageMetaData?.metaList.map((meta) => (
                        <TableRow key={meta.id}>
                          <TableCell className="font-mono text-sm">
                            {meta.path}
                          </TableCell>
                          <TableCell className="max-w-xs truncate">
                            {meta.title || '无标题'}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{meta.language}</Badge>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(meta.isActive)}
                          </TableCell>
                          <TableCell>
                            {getIndexBadge(meta.noIndex)}
                          </TableCell>
                          <TableCell className="text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {formatDate(meta.createdAt)}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center gap-2 justify-end">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openEditDialog(meta)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDelete(meta)}
                                className="text-destructive hover:text-destructive"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* 分页 */}
                  {pageMetaData && pageMetaData.pagination.pages > 1 && (
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        共 {pageMetaData.pagination.total} 个配置，
                        第 {pageMetaData.pagination.page} 页 / 共 {pageMetaData.pagination.pages} 页
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page - 1)}
                          disabled={pagination.page <= 1}
                        >
                          上一页
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(pagination.page + 1)}
                          disabled={pagination.page >= pagination.pages}
                        >
                          下一页
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}

      {/* 新增/编辑对话框 */}
      <Dialog open={isDialogOpen} onOpenChange={closeDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingMeta ? '编辑SEO配置' : '新建SEO配置'}
            </DialogTitle>
            <DialogDescription>
              配置页面的SEO元数据，包括标题、描述、关键词等信息
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">
                <Settings className="h-4 w-4 mr-2" />
                基础设置
              </TabsTrigger>
              <TabsTrigger value="jsonld">
                <Code className="h-4 w-4 mr-2" />
                JSON-LD
              </TabsTrigger>
              <TabsTrigger value="opengraph">
                <Share2 className="h-4 w-4 mr-2" />
                OpenGraph
              </TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="path">页面路径 *</Label>
                  <Input
                    id="path"
                    placeholder="例如：/about"
                    value={formData.path}
                    onChange={(e) => handleInputChange('path', e.target.value)}
                    aria-label="页面路1径23123123"
                  />
                </div>
                <div>
                  <Label htmlFor="language">语言</Label>
                  <Select value={formData.language} onValueChange={(value) => handleInputChange('language', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="zh-CN">中文</SelectItem>
                      <SelectItem value="en">英文</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="title">页面标题</Label>
                <Input
                  id="title"
                  placeholder="页面标题，建议60字符以内"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="description">页面描述</Label>
                <Textarea
                  id="description"
                  placeholder="页面描述，建议160字符以内"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="keywords">关键词</Label>
                <Input
                  id="keywords"
                  placeholder="关键词，用逗号分隔"
                  value={formData.keywords}
                  onChange={(e) => handleInputChange('keywords', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="canonical">规范链接</Label>
                <Input
                  id="canonical"
                  placeholder="规范链接URL"
                  value={formData.canonical}
                  onChange={(e) => handleInputChange('canonical', e.target.value)}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  />
                  <Label htmlFor="isActive">启用配置</Label>
                </div>
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="noIndex"
                    checked={formData.noIndex}
                    onChange={(e) => handleInputChange('noIndex', e.target.checked)}
                  />
                  <Label htmlFor="noIndex">禁止索引</Label>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="jsonld">
              <JsonLdEditor
                value={formData.jsonLd}
                onChange={(value) => handleInputChange('jsonLd', value)}
                disabled={saving}
              />
            </TabsContent>

            <TabsContent value="opengraph">
              <OpenGraphEditor
                value={formData.openGraph}
                onChange={(value) => handleInputChange('openGraph', value)}
                disabled={saving}
              />
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={closeDialog} disabled={saving}>
              取消
            </Button>
            <Button onClick={handleSubmit} disabled={saving || !formData.path}>
              {saving ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : editingMeta ? (
                <Edit className="mr-2 h-4 w-4" />
              ) : (
                <Plus className="mr-2 h-4 w-4" />
              )}
              {saving ? '保存中...' : editingMeta ? '更新配置' : '创建配置'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 