"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import { BlogPostForm } from '@/components/ui/blog-post-form';
import { 
  ArrowLeft,
  Database
} from 'lucide-react';
import { Project } from '@/types/project';
import { BlogFormData } from '@/types/blog';

export default function NewBlogPostPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
  };

  // 初始化项目ID
  useEffect(() => {
    const projectIdParam = searchParams.get('projectId');
    if (projectIdParam && !selectedProject) {
      // 项目选择器会自动处理
    }
  }, [searchParams]);

  const handleSubmit = async (formData: BlogFormData, status: string) => {
    if (!selectedProject) {
      setError('请先选择一个项目');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const response = await fetch('/api/content/blog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: selectedProject.id,
          ...formData,
          status,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          router.push(`/admin/content/blog?projectId=${selectedProject.id}`);
        } else {
          setError(result.error || '创建文章失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('提交文章时出错');
      console.error('Submit post error:', err);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="新建文章"
        description="创建一篇新的博客文章"
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
        prefixElement={
          <Button variant="ghost" size="sm" asChild>
            <a href={selectedProject ? `/admin/content/blog?projectId=${selectedProject.id}` : '/admin/content/blog'}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回列表
            </a>
          </Button>
        }
        error={error}
      />

      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来创建文章</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <BlogPostForm
          project={selectedProject}
          mode="create"
          onSubmit={handleSubmit}
          saving={saving}
          error={error}
        />
      )}
    </div>
  );
} 