"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Loader2,
  AlertCircle,
  Database
} from 'lucide-react';
import { Project } from '@/types/project';
import { BlogCategory, BlogPostWithCategory } from '@/types/blog';

export default function BlogManagementPage() {
  const searchParams = useSearchParams();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [posts, setPosts] = useState<BlogPostWithCategory[]>([]);
  const [categories, setCategories] = useState<BlogCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  
  // 筛选状态
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('全部');
  const [categoryFilter, setCategoryFilter] = useState('全部');

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
    loadBlogData(projectId);
    loadCategories(projectId);
  };

  const loadBlogData = async (projectId: number, page = 1) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        projectId: projectId.toString(),
        page: page.toString(),
        limit: pagination.limit.toString(),
      });
      
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter !== '全部') params.append('status', statusFilter);
      if (categoryFilter !== '全部') params.append('category', categoryFilter);

      const response = await fetch(`/api/content/blog?${params}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setPosts(result.data.posts || []);
          setPagination(result.data.pagination || pagination);
        } else {
          setError(result.error || '获取博客数据失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('加载博客数据时出错');
      console.error('Load blog data error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async (projectId: number) => {
    try {
      const response = await fetch(`/api/content/categories?projectId=${projectId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data || []);
        }
      }
    } catch (err) {
      console.error('Load categories error:', err);
    }
  };

  // 监听搜索和筛选条件变化
  useEffect(() => {
    if (selectedProject) {
      const timer = setTimeout(() => {
        loadBlogData(selectedProject.id, 1);
      }, 500); // 防抖
      return () => clearTimeout(timer);
    }
  }, [searchTerm, statusFilter, categoryFilter]);

  // 初始化项目ID
  useEffect(() => {
    const projectIdParam = searchParams.get('projectId');
    if (projectIdParam && !selectedProject) {
      // 项目选择器会自动处理
    }
  }, [searchParams]);

  const handleSearch = () => {
    if (selectedProject) {
      loadBlogData(selectedProject.id, 1);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (selectedProject) {
      loadBlogData(selectedProject.id, newPage);
    }
  };

  const formatDate = (dateString?: string) => {
    return new Date(dateString || '').toLocaleDateString();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="default">已发布</Badge>;
      case 'draft':
        return <Badge variant="secondary">草稿</Badge>;
      case 'archived':
        return <Badge variant="outline">已归档</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="博客管理"
        description="管理博客文章、编辑内容和发布设置"
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
        actions={
          selectedProject && (
            <Button asChild>
              <Link href={`/admin/content/blog/new?projectId=${selectedProject.id}`}>
                <Plus className="mr-2 h-4 w-4" />
                新建文章
              </Link>
            </Button>
          )
        }
      />

      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来管理博客内容</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* 筛选和搜索 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                筛选和搜索
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input 
                    placeholder="搜索文章标题..." 
                    className="pl-8" 
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="全部">全部分类</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="全部">全部状态</SelectItem>
                    <SelectItem value="published">已发布</SelectItem>
                    <SelectItem value="draft">草稿</SelectItem>
                    <SelectItem value="archived">已归档</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button onClick={handleSearch} disabled={loading}>
                  {loading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="mr-2 h-4 w-4" />
                  )}
                  搜索
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 错误提示 */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="flex items-center gap-2 py-4">
                <AlertCircle className="h-5 w-5 text-destructive" />
                <span className="text-destructive">{error}</span>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => selectedProject && loadBlogData(selectedProject.id)}
                  className="ml-auto"
                >
                  重试
                </Button>
              </CardContent>
            </Card>
          )}

          {/* 文章列表 */}
          <Card>
            <CardHeader>
              <CardTitle>文章列表</CardTitle>
              <CardDescription>
                {loading ? '加载中...' : `共 ${pagination.total} 篇文章`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <p className="text-muted-foreground">加载文章列表中...</p>
                  </div>
                </div>
              ) : posts.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-medium mb-2">暂无文章</p>
                  <p>开始创建您的第一篇博客文章吧</p>
                  <Button asChild className="mt-4">
                    <Link href={`/admin/content/blog/new?projectId=${selectedProject.id}`}>
                      <Plus className="mr-2 h-4 w-4" />
                      新建文章
                    </Link>
                  </Button>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>文章信息</TableHead>
                        <TableHead>分类</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>数据</TableHead>
                        <TableHead>时间</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {posts.map((post) => (
                        <TableRow key={post.id}>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">{post.title}</div>
                              {post.summary && (
                                <div className="text-sm text-muted-foreground line-clamp-2">
                                  {post.summary}
                                </div>
                              )}
                              <div className="text-xs text-muted-foreground flex items-center gap-2">
                                <User className="h-3 w-3" />
                                {post.authorName || '未知作者'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {post.categoryName && (
                              <Badge variant="outline">{post.categoryName}</Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getStatusBadge(post.status)}
                              {post.isTop && (
                                <Badge variant="destructive" className="text-xs">置顶</Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm space-y-1">
                              <div className="flex items-center gap-1">
                                <Eye className="h-3 w-3" />
                                <span>{post.viewCount}</span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm space-y-1">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(post.createdAt)}</span>
                              </div>
                              {post.publishedAt && (
                                <div className="text-xs text-muted-foreground">
                                  发布于 {formatDate(post.publishedAt)}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center gap-1 justify-end">
                              <Button asChild variant="ghost" size="sm">
                                <Link href={`/admin/content/blog/${post.id}/edit?projectId=${selectedProject.id}`}>
                                  <Edit className="h-4 w-4" />
                                </Link>
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* 分页 */}
                  {pagination.pages > 1 && (
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-sm text-muted-foreground">
                        第 {pagination.page} 页，共 {pagination.pages} 页
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={pagination.page <= 1}
                          onClick={() => handlePageChange(pagination.page - 1)}
                        >
                          上一页
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={pagination.page >= pagination.pages}
                          onClick={() => handlePageChange(pagination.page + 1)}
                        >
                          下一页
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
} 