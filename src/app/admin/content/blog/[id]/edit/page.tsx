"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import { BlogPostForm } from '@/components/ui/blog-post-form';
import { 
  ArrowLeft,
  Loader2,
  Database,
  AlertCircle
} from 'lucide-react';
import { Project } from '@/types/project';
import { BlogPostWithCategory } from '@/types/blog';

export default function EditBlogPostPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const postId = params.id as string;
  
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [blogPost, setBlogPost] = useState<BlogPostWithCategory | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
    loadBlogPost(projectId, postId);
  };

  const loadBlogPost = async (projectId: number, id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/content/blog/${id}?projectId=${projectId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBlogPost(result.data);
        } else {
          setError(result.error || '获取文章数据失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('加载文章数据时出错');
      console.error('Load blog post error:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初始化项目ID
  useEffect(() => {
    const projectIdParam = searchParams.get('projectId');
    if (projectIdParam && !selectedProject) {
      // 项目选择器会自动处理
    }
  }, [searchParams]);

  const handleSubmit = async (formData: Omit<BlogPostWithCategory, 'id' | 'isTop' | 'viewCount' | 'likeCount' | 'publishedAt' | 'createdAt' | 'updatedAt' | 'categoryName' | 'categorySlug'>, status: string) => {
    if (!selectedProject || !blogPost) {
      setError('缺少必要的数据');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const response = await fetch(`/api/content/blog/${postId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: selectedProject.id,
          ...formData,
          status,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          router.push(`/admin/content/blog?projectId=${selectedProject.id}`);
        } else {
          setError(result.error || '更新文章失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('提交文章时出错');
      console.error('Submit post error:', err);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="编辑文章"
        description={blogPost ? `编辑文章：${blogPost.title}` : '编辑博客文章'}
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
        prefixElement={
          <Button variant="ghost" size="sm" asChild>
            <a href={selectedProject ? `/admin/content/blog?projectId=${selectedProject.id}` : '/admin/content/blog'}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回列表
            </a>
          </Button>
        }
        error={error}
      />

      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来编辑文章</p>
            </div>
          </CardContent>
        </Card>
      ) : loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">加载文章数据中...</p>
            </div>
          </CardContent>
        </Card>
      ) : !blogPost ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <AlertCircle className="h-12 w-12 mx-auto mb-4" />
              <p>文章不存在或加载失败</p>
              <Button 
                variant="outline" 
                onClick={() => loadBlogPost(selectedProject.id, postId)}
                className="mt-4"
              >
                重试
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <BlogPostForm
          project={selectedProject}
          initialData={{
            title: blogPost.title,
            slug: blogPost.slug,
            summary: blogPost.summary || '',
            content: blogPost.content,
            categoryId: blogPost.categoryId,
            tags: blogPost.tags,
            status: blogPost.status,
            metaTitle: blogPost.metaTitle,
            metaDescription: blogPost.metaDescription,
            metaKeywords: blogPost.metaKeywords,
            authorName: blogPost.authorName,
            authorEmail: blogPost.authorEmail,
          }}
          mode="edit"
          onSubmit={handleSubmit}
          saving={saving}
          error={error}
        />
      )}
    </div>
  );
} 