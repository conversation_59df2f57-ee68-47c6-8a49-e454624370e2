"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import { ConfigEditorDialog } from '@/components/ui/config-editor-dialog';
import { ThemeSelectorDialog } from '@/components/ui/theme-selector-dialog';
import { 
  FileText, 
  Tags, 
  Settings, 
  Plus,
  BarChart3,
  Eye,
  Edit3,
  Loader2,
  Check,
  X
} from 'lucide-react';
import { Project } from '@/types/project';

interface ContentStats {
  blogPosts: number;
  categories: number;
  tdkConfigs: number;
}

interface ConfigStatus {
  'robots.txt': boolean;
  'llms.txt': boolean;
  'llms-full.txt': boolean;
  'blog-theme': boolean;
}

export default function ContentManagementPage() {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [contentStats, setContentStats] = useState<ContentStats | null>(null);
  const [configStatus, setConfigStatus] = useState<ConfigStatus>({
    'robots.txt': false,
    'llms.txt': false,
    'llms-full.txt': false,
    'blog-theme': false,
  });
  const [recentPosts, setRecentPosts] = useState<Array<{
    id: number;
    title: string;
    status: string;
    categoryName?: string;
    authorName?: string;
    publishedAt?: string;
    viewCount: number;
  }>>([]);
  const [loading, setLoading] = useState(false);
  const [configDialog, setConfigDialog] = useState<{
    open: boolean;
    configKey: string;
    title: string;
    description: string;
  }>({
    open: false,
    configKey: '',
    title: '',
    description: '',
  });
  const [themeDialog, setThemeDialog] = useState(false);

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
    loadContentData(projectId);
  };

  const loadContentData = async (projectId: number) => {
    setLoading(true);
    try {
      // 加载博客统计
      let blogCount = 0;
      const blogResponse = await fetch(`/api/content/blog?projectId=${projectId}&limit=5`);
      if (blogResponse.ok) {
        const blogResult = await blogResponse.json();
        if (blogResult.success) {
          setRecentPosts(blogResult.data.posts || []);
          blogCount = blogResult.data?.pagination?.total || 0;
        }
      }

      // 加载分类统计
      const categoriesResponse = await fetch(`/api/content/categories?projectId=${projectId}`);
      let categoriesCount = 0;
      if (categoriesResponse.ok) {
        const categoriesResult = await categoriesResponse.json();
        if (categoriesResult.success) {
          categoriesCount = categoriesResult.data?.length || 0;
        }
      }

      // 加载页面元数据统计
      const pageMetaResponse = await fetch(`/api/content/page-meta?projectId=${projectId}&limit=1`);
      let pageMetaCount = 0;
      if (pageMetaResponse.ok) {
        const pageMetaResult = await pageMetaResponse.json();
        if (pageMetaResult.success) {
          pageMetaCount = pageMetaResult.data?.pagination?.total || 0;
        }
      }
      loadConfigStatus(projectId);
      // 更新统计数据
      setContentStats({
        blogPosts: blogCount,
        categories: categoriesCount,
        tdkConfigs: pageMetaCount,
      });
    } catch (error) {
      console.error('Load content data error:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadConfigStatus = async (projectId: number) => { 
          // 加载基础配置状态
      const configKeys = 'robots.txt,llms.txt,llms-full.txt,blog-theme';
      const configResponse = await fetch(`/api/content/site-config?projectId=${projectId}&keys=${configKeys}`);
      if (configResponse.ok) {
        const configResult = await configResponse.json();
        if (configResult.success) {
          const configs = configResult.data;
          setConfigStatus({
            'robots.txt': !!configs['robots.txt'],
            'llms.txt': !!configs['llms.txt'],
            'llms-full.txt': !!configs['llms-full.txt'],
            'blog-theme': !!configs['blog-theme'],
          });
        }
      }
  }

  // 打开配置编辑弹窗
  const openConfigDialog = (configKey: string, title: string, description: string) => {
    setConfigDialog({
      open: true,
      configKey,
      title,
      description,
    });
  };

  // 配置保存后的回调
  const handleConfigSaved = () => {
    if (selectedProject) {
      loadConfigStatus(selectedProject.id);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="内容管理"
        description="管理网站内容、博客文章、媒体文件和SEO配置"
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
      />
      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来管理内容</p>
            </div>
          </CardContent>
        </Card>
      ) : loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">加载内容数据中...</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* 内容统计卡片 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">基础配置</CardTitle>
                <div className="p-2 rounded-md bg-purple-500">
                  <Settings className="h-4 w-4 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="justify-between"
                    onClick={() => openConfigDialog('robots.txt', 'Robots.txt', '搜索引擎爬虫控制文件')}
                  >
                    <span>robots.txt</span>
                    {configStatus['robots.txt'] ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-red-500" />
                    )}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="justify-between"
                    onClick={() => openConfigDialog('llms.txt', 'LLMs.txt', 'AI大模型爬虫控制文件')}
                  >
                    <span>llms.txt</span>
                    {configStatus['llms.txt'] ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-red-500" />
                    )}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="justify-between"
                    onClick={() => openConfigDialog('llms-full.txt', 'LLMs-full.txt', '完整版AI大模型配置文件')}
                  >
                    <span>llms-full.txt</span>
                    {configStatus['llms-full.txt'] ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-red-500" />
                    )}
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    className="justify-between"
                    onClick={() => setThemeDialog(true)}
                  >
                    <span>blog-theme</span>
                    {configStatus['blog-theme'] ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <X className="h-4 w-4 text-red-500" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">博客文章</CardTitle>
                <div className="p-2 rounded-md bg-blue-500">
                  <FileText className="h-4 w-4 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{contentStats?.blogPosts || 0}</div>
                <Button asChild variant="outline" size="sm" className="w-full mt-3">
                  <Link href={`/admin/content/blog?projectId=${selectedProject.id}`}>
                    管理博客文章
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">文章分类</CardTitle>
                <div className="p-2 rounded-md bg-green-500">
                  <Tags className="h-4 w-4 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{contentStats?.categories || 0}</div>
                <Button asChild variant="outline" size="sm" className="w-full mt-3">
                  <Link href={`/admin/content/categories?projectId=${selectedProject.id}`}>
                    管理分类标签
                  </Link>
                </Button>
              </CardContent>
            </Card>
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Meta配置</CardTitle>
                <div className="p-2 rounded-md bg-orange-500">
                  <Settings className="h-4 w-4 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{contentStats?.tdkConfigs || 0}</div>
                <Button asChild variant="outline" size="sm" className="w-full mt-3">
                  <Link href={`/admin/content/page-meta?projectId=${selectedProject.id}`}>
                    管理SEO配置
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {/* 最近文章 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  最近文章
                </CardTitle>
                <CardDescription>
                  最新发布和编辑的博客文章
                </CardDescription>
              </CardHeader>
              <CardContent>
                {recentPosts.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-8 w-8 mx-auto mb-2" />
                    <p>暂无文章</p>
                  </div>
                ) : (
                  <>
                    <div className="space-y-4">
                      {recentPosts.map((post) => (
                        <div key={post.id} className="flex items-start justify-between p-3 rounded-lg border">
                          <div className="space-y-1 flex-1">
                            <div className="flex items-center gap-2">
                              <h4 className="text-sm font-medium leading-none">
                                {post.title}
                              </h4>
                              <Badge 
                                variant={post.status === 'published' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {post.status === 'published' ? '已发布' : '草稿'}
                              </Badge>
                            </div>
                            <p className="text-xs text-muted-foreground">
                              {post.categoryName} • {post.authorName}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              {post.publishedAt && (
                                <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
                              )}
                              {post.status === 'published' && (
                                <div className="flex items-center gap-1">
                                  <Eye className="h-3 w-3" />
                                  <span>{post.viewCount}</span>
                                </div>
                              )}
                            </div>
                          </div>
                          <Button asChild variant="ghost" size="sm">
                            <Link href={`/admin/content/blog/${post.id}?projectId=${selectedProject.id}`}>
                              <Edit3 className="h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      ))}
                    </div>
                    <Button asChild variant="outline" className="w-full mt-4">
                      <Link href={`/admin/content/blog?projectId=${selectedProject.id}`}>
                        查看所有文章
                      </Link>
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  快速操作
                </CardTitle>
                <CardDescription>
                  常用的内容管理操作
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href={`/admin/content/blog/new?projectId=${selectedProject.id}`}>
                      <Plus className="mr-2 h-4 w-4" />
                      创建新文章
                    </Link>
                  </Button>
                  
                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href={`/admin/content/categories?projectId=${selectedProject.id}`}>
                      <Tags className="mr-2 h-4 w-4" />
                      管理分类和标签
                    </Link>
                  </Button>
                
                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href={`/admin/content/page-meta?projectId=${selectedProject.id}`}>
                      <Settings className="mr-2 h-4 w-4" />
                      配置页面SEO
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {/* 配置编辑弹窗 */}
      <ConfigEditorDialog
        open={configDialog.open}
        onOpenChange={(open) => setConfigDialog({ ...configDialog, open })}
        configKey={configDialog.configKey}
        title={configDialog.title}
        description={configDialog.description}
        projectId={selectedProject?.id || 0}
        domain={selectedProject?.domain || ''}
        onSave={handleConfigSaved}
      />

      {/* 主题选择弹窗 */}
      <ThemeSelectorDialog
        open={themeDialog}
        onOpenChange={setThemeDialog}
        projectId={selectedProject?.id || 0}
        onSave={handleConfigSaved}
      />
    </div>
  );
} 