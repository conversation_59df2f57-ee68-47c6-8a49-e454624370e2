"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams, useParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import { FaqForm } from '@/components/ui/faq-form';
import { 
  ArrowLeft,
  Loader2,
  Database,
  AlertCircle
} from 'lucide-react';
import { Project } from '@/types/project';
import { FaqType } from '@/types/faq';

export default function EditFaqPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const faqId = params.id as string;
  
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [faq, setFaq] = useState<FaqType | null>(null);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
    loadFaq(projectId, faqId);
  };

  const loadFaq = async (projectId: number, id: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/content/faq/${id}?projectId=${projectId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setFaq(result.data);
        } else {
          setError(result.error || '获取FAQ数据失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('加载FAQ数据时出错');
      console.error('Load FAQ error:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初始化项目ID
  useEffect(() => {
    const projectIdParam = searchParams.get('projectId');
    if (projectIdParam && !selectedProject) {
      // 项目选择器会自动处理
    }
  }, [searchParams]);

  const handleSubmit = async (formData: { question: string; answer: string; categoryId?: number }) => {
    if (!selectedProject || !faq) {
      setError('缺少必要的数据');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const response = await fetch(`/api/content/faq/${faqId}?projectId=${selectedProject.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          router.push(`/admin/content/faq?projectId=${selectedProject.id}`);
        } else {
          setError(result.error || '更新FAQ失败');
        }
      } else {
        setError('网络请求失败');
      }
    } catch (err) {
      setError('提交FAQ时出错');
      console.error('Submit FAQ error:', err);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="编辑FAQ"
        description={faq ? `编辑FAQ：${faq.question}` : '编辑FAQ'}
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
        prefixElement={
          <Button variant="ghost" size="sm" asChild>
            <a href={selectedProject ? `/admin/content/faq?projectId=${selectedProject.id}` : '/admin/content/faq'}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回列表
            </a>
          </Button>
        }
        error={error}
      />

      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来编辑FAQ</p>
            </div>
          </CardContent>
        </Card>
      ) : loading ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-muted-foreground">加载FAQ数据中...</p>
            </div>
          </CardContent>
        </Card>
      ) : !faq ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <AlertCircle className="h-12 w-12 mx-auto mb-4" />
              <p>FAQ不存在或加载失败</p>
              <Button 
                variant="outline" 
                onClick={() => loadFaq(selectedProject.id, faqId)}
                className="mt-4"
              >
                重试
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <FaqForm
          project={selectedProject}
          initialData={{
            question: faq.question,
            answer: faq.answer || "",
            categoryId: faq.categoryId || undefined,
          }}
          mode="edit"
          onSubmit={handleSubmit}
          saving={saving}
          error={error}
        />
      )}
    </div>
  );
}