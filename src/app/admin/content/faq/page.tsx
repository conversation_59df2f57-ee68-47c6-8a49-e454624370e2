"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FaqAnswerPreview } from '@/components/ui/faq-rich-text-display';
import { Input } from '@/components/ui/input';
import { PageHeaderWithProject } from '@/components/ui/page-header-with-project';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  HelpCircle,
  Loader2,
  AlertCircle,
  Database
} from 'lucide-react';
import { Project } from '@/types/project';
import { FaqCategoryType, FaqType, FAQ_STATUS_OPTIONS } from '@/types/faq';

export default function FaqPage() {
  const searchParams = useSearchParams();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [faqs, setFaqs] = useState<FaqType[]>([]);
  const [faqCategories, setFaqCategories] = useState<FaqCategoryType[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });

  // 筛选状态
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("全部");
  const [statusFilter, setStatusFilter] = useState("");
  const [sortBy, setSortBy] = useState("sort");
  const [sortOrder, setSortOrder] = useState("asc");

  const handleProjectChange = (projectId: number, project: Project) => {
    setSelectedProject(project);
    loadFaqData(projectId);
    loadFaqCategories(projectId);
  };

  const loadFaqData = async (projectId: number, page = 1) => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        projectId: projectId.toString(),
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy: sortBy,
        sortOrder: sortOrder,
      });
      if (searchTerm) params.append("search", searchTerm);
      if (categoryFilter !== "全部") params.append("category", categoryFilter);
      if (statusFilter) params.append("status", statusFilter);

      const response = await fetch(`/api/content/faq?${params}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setFaqs(result.data || []);
          setPagination(result.pagination || pagination);
        } else {
          setError(result.error || "获取FAQ数据失败");
        }
      } else {
        setError("网络请求失败");
      }
    } catch (err) {
      setError("加载FAQ数据时出错");
      console.error("Load FAQ data error:", err);
    } finally {
      setLoading(false);
    }
  };

  const loadFaqCategories = async (projectId: number) => {
    try {
      const response = await fetch(
        `/api/content/faq-categories?projectId=${projectId}`
      );
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setFaqCategories(result.data || []);
        }
      }
    } catch (err) {
      console.error("Load FAQ categories error:", err);
    }
  };

  // 监听搜索和筛选条件变化
  useEffect(() => {
    if (selectedProject) {
      const timer = setTimeout(() => {
        loadFaqData(selectedProject.id, 1);
      }, 500); // 防抖
      return () => clearTimeout(timer);
    }
  }, [searchTerm, categoryFilter, statusFilter, sortBy, sortOrder]);

  // 初始化项目ID
  useEffect(() => {
    const projectIdParam = searchParams.get('projectId');
    if (projectIdParam && !selectedProject) {
      // 项目选择器会自动处理
    }
  }, [searchParams]);

  const handleSearch = () => {
    if (selectedProject) {
      loadFaqData(selectedProject.id, 1);
    }
  };

  const handlePageChange = (newPage: number) => {
    if (selectedProject) {
      loadFaqData(selectedProject.id, newPage);
    }
  };



  const handleDelete = async (faqId: number) => {
    if (!selectedProject || !confirm('确定删除这个FAQ吗？')) return;
    
    try {
      const response = await fetch(`/api/content/faq/${faqId}?projectId=${selectedProject.id}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        loadFaqData(selectedProject.id, pagination.page);
      } else {
        alert('删除失败');
      }
    } catch (err) {
      console.error('Delete FAQ error:', err);
      alert('删除时出错');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面头部组件 */}
      <PageHeaderWithProject
        title="FAQ管理"
        description="管理FAQ、编辑FAQ内容和发布设置"
        selectedProject={selectedProject}
        onProjectChange={handleProjectChange}
        actions={
          selectedProject && (
            <Button asChild>
              <Link
                href={`/admin/content/faq/new?projectId=${selectedProject.id}`}
              >
                <Plus className="mr-2 h-4 w-4" />
                新建FAQ
              </Link>
            </Button>
          )
        }
      />

      {!selectedProject ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4" />
              <p>请先选择一个项目来管理FAQ内容</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* 筛选和搜索 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                筛选和搜索
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索FAQ问题..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="全部">全部分类</SelectItem>
                    {faqCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* <SelectItem value="">全部状态</SelectItem> */}
                    {FAQ_STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button onClick={handleSearch} disabled={loading}>
                  {loading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="mr-2 h-4 w-4" />
                  )}
                  搜索
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 错误提示 */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="flex items-center gap-2 py-4">
                <AlertCircle className="h-5 w-5 text-destructive" />
                <span className="text-destructive">{error}</span>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => selectedProject && loadFaqData(selectedProject.id)}
                  className="ml-auto"
                >
                  重试
                </Button>
              </CardContent>
            </Card>
          )}

          {/* FAQ列表 */}
          <Card>
            <CardHeader>
              <CardTitle>FAQ列表</CardTitle>
              <CardDescription>
                {loading ? '加载中...' : `共 ${pagination.total} 个FAQ`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                    <p className="text-muted-foreground">加载FAQ列表中...</p>
                  </div>
                </div>
              ) : faqs.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <HelpCircle className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-medium mb-2">暂无FAQ</p>
                  <p>开始创建您的第一个FAQ吧</p>
                  <Button asChild className="mt-4">
                    <Link href={`/admin/content/faq/new?projectId=${selectedProject.id}`}>
                      <Plus className="mr-2 h-4 w-4" />
                      新建FAQ
                    </Link>
                  </Button>
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>问题</TableHead>
                        <TableHead>答案</TableHead>
                        <TableHead>分类</TableHead>
                        <TableHead>状态</TableHead>
                        <TableHead>排序</TableHead>
                        <TableHead>创建时间</TableHead>
                        <TableHead className="text-right">操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {faqs.map((faq) => (
                        <TableRow key={faq.id}>
                          <TableCell>
                            <div className="font-medium max-w-xs truncate">
                              {faq.question}
                            </div>
                          </TableCell>
                          <TableCell>
                            <FaqAnswerPreview content={faq.answer || ""} />
                          </TableCell>
                          <TableCell>
                            {faq.categoryId && (
                              <Badge variant="outline">
                                {faqCategories.find(cat => cat.id === faq.categoryId)?.name || '未知分类'}
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                faq.status === 'published' ? 'default' :
                                faq.status === 'draft' ? 'secondary' :
                                'destructive'
                              }
                            >
                              {FAQ_STATUS_OPTIONS.find(opt => opt.value === faq.status)?.label || faq.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm font-mono">
                              {faq.sort}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {faq.createdAt?.toLocaleString()}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center gap-1 justify-end">
                              <Button asChild variant="ghost" size="sm">
                                <Link href={`/admin/content/faq/${faq.id}/edit?projectId=${selectedProject.id}`}>
                                  <Edit className="h-4 w-4" />
                                </Link>
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleDelete(faq.id)}>
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* 分页 */}
                  {pagination.pages > 1 && (
                    <div className="flex items-center justify-between mt-4">
                      <div className="text-sm text-muted-foreground">
                        第 {pagination.page} 页，共 {pagination.pages} 页
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={pagination.page <= 1}
                          onClick={() => handlePageChange(pagination.page - 1)}
                        >
                          上一页
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled={pagination.page >= pagination.pages}
                          onClick={() => handlePageChange(pagination.page + 1)}
                        >
                          下一页
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
