"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Plus,
  Database,
  Trash2,
  Edit,
  TestTube,
  Loader2,
  Eye,
  FileText,
  AlertCircle
} from 'lucide-react';
import { Project } from '@/types/project';
import { ImageUpload } from '@/components/ui/image-upload';
import { Al<PERSON>, AlertDescription } from '@/components/ui/alert';

interface ProjectFormData {
  name: string;
  description: string;
  domain: string;
  logo?: string;
  dbHost: string;
  dbPort: number;
  dbName: string;
  dbUser: string;
  dbPassword: string;
  isActive?: boolean;
  config?: {
    s3?: {
      bucket: string;
      cdn: string;
    };
  };
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// API调用函数
const projectsApi = {
  // 获取项目列表
  getProjects: async (): Promise<ApiResponse<Project[]>> => {
    try {
      const response = await fetch('/api/projects');
      return await response.json();
    } catch {
      return { success: false, error: '获取项目列表失败' };
    }
  },

  // 创建项目
  createProject: async (data: ProjectFormData): Promise<ApiResponse<Project>> => {
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      return await response.json();
    } catch {
      return { success: false, error: '创建项目失败' };
    }
  },

  // 更新项目
  updateProject: async (id: number, data: Partial<ProjectFormData>): Promise<ApiResponse<Project>> => {
    try {
      const response = await fetch('/api/projects', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, ...data }),
      });
      return await response.json();
    } catch {
      return { success: false, error: '更新项目失败' };
    }
  },

  // 删除项目
  deleteProject: async (id: number): Promise<ApiResponse<{ id: number; name: string }>> => {
    try {
      const response = await fetch(`/api/projects?id=${id}`, {
        method: 'DELETE',
      });
      return await response.json();
    } catch {
      return { success: false, error: '删除项目失败' };
    }
  },

  // 测试数据库连接
  testConnection: async (config: {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
  }): Promise<ApiResponse<void>> => {
    try {
      const response = await fetch('/api/projects/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });
      return await response.json();
    } catch {
      return { success: false, error: '测试连接失败' };
    }
  },

  // 检查表结构
  checkTables: async (projectId: number): Promise<ApiResponse<{
    projectId: number;
    projectName: string;
    database: string;
    tables: Array<{
      name: string;
      type: string;
      comment?: string;
      columns: Array<{
        name: string;
        type: string;
        nullable: boolean;
        default?: string;
        maxLength?: number;
        precision?: number;
        scale?: number;
        position: number;
      }>;
      primaryKeys: string[];
      foreignKeys: Array<{
        column: string;
        referencedTable: string;
        referencedColumn: string;
      }>;
      indexes: Array<{
        name: string;
        definition: string;
      }>;
    }>;
    totalTables: number;
    requiredTables?: string[];
    missingTables?: Array<{
      name: string;
      displayName: string;
      description: string;
    }>;
    extraTables?: string[];
    needsSetup?: boolean;
  }>> => {
    try {
      const response = await fetch(`/api/projects/${projectId}/check-tables`);
      return await response.json();
    } catch {
      return { success: false, error: '检查表结构失败' };
    }
  },

  // 创建缺失的表
  createTables: async (projectId: number, tablesToCreate: string[]): Promise<ApiResponse<{
    projectId: number;
    projectName: string;
    database: string;
    createdTables: Array<{
      name: string;
      displayName: string;
      success: boolean;
      error?: string;
    }>;
    summary: {
      total: number;
      success: number;
      failed: number;
    };
  }>> => {
    try {
      const response = await fetch(`/api/projects/${projectId}/create-tables`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tablesToCreate }),
      });
      return await response.json();
    } catch {
      return { success: false, error: '创建表结构失败' };
    }
  },

  // 测试加密解密
  testCrypto: async (password: string): Promise<ApiResponse<{
    original: string;
    encrypted: string;
    decrypted: string;
    isMatch: boolean;
    encryptionKeyLength: number;
    isDefaultKey: boolean;
  }>> => {
    try {
      const response = await fetch('/api/projects/test-crypto', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });
      return await response.json();
    } catch {
      return { success: false, error: '测试加密解密失败' };
    }
  },
};

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [testingConnection, setTestingConnection] = useState<number | null>(null);
  const [isTestingNewConnection, setIsTestingNewConnection] = useState(false);
  const [isTableStructureDialogOpen, setIsTableStructureDialogOpen] = useState(false);
  const [tableStructureData, setTableStructureData] = useState<{
    projectId: number;
    projectName: string;
    database: string;
    tables: Array<{
      name: string;
      type: string;
      comment?: string;
      columns: Array<{
        name: string;
        type: string;
        nullable: boolean;
        default?: string;
        maxLength?: number;
        precision?: number;
        scale?: number;
        position: number;
      }>;
      primaryKeys: string[];
      foreignKeys: Array<{
        column: string;
        referencedTable: string;
        referencedColumn: string;
      }>;
      indexes: Array<{
        name: string;
        definition: string;
      }>;
    }>;
    totalTables: number;
    requiredTables?: string[];
    missingTables?: Array<{
      name: string;
      displayName: string;
      description: string;
    }>;
    extraTables?: string[];
    needsSetup?: boolean;
  } | null>(null);
  const [loadingTableStructure, setLoadingTableStructure] = useState<number | null>(null);;
  const [creatingTables, setCreatingTables] = useState(false);
  const [selectedTablesToCreate, setSelectedTablesToCreate] = useState<string[]>([]);
  const [formData, setFormData] = useState<ProjectFormData>({
    name: '',
    description: '',
    domain: '',
    logo: '',
    dbHost: '',
    dbPort: 5432,
    dbName: '',
    dbUser: '',
    dbPassword: '',
    config: { s3: { bucket: '', cdn: '' } },
  });
  const [alertInfo, setAlertInfo] = useState<{ message: string; type?: 'default' | 'destructive' } | null>(null);

  // 加载项目列表
  const loadProjects = async () => {
    setLoading(true);
    const response = await projectsApi.getProjects();
    if (response.success && response.data) {
      setProjects(response.data);
    } else {
      setAlertInfo({ message: response.error || '加载项目列表失败', type: 'destructive' });
    }
    setLoading(false);
  };

  // 组件加载时获取数据
  useEffect(() => {
    loadProjects();
  }, []);

  const handleInputChange = (field: keyof ProjectFormData, value: string | number | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // s3输入项变更，保证 bucket/cdn 为 string
  const handleS3Change = (key: 'bucket' | 'cdn', value: string) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        s3: {
          bucket: key === 'bucket' ? value : prev.config?.s3?.bucket ?? '',
          cdn: key === 'cdn' ? value : prev.config?.s3?.cdn ?? '',
        }
      },
    }));
  };

  // 测试新项目的数据库连接
  const handleTestNewConnection = async () => {
    if (!formData.dbHost || !formData.dbName || !formData.dbUser || !formData.dbPassword) {
      setAlertInfo({ message: '请填写完整的数据库连接信息', type: 'destructive' });
      return;
    }

    setIsTestingNewConnection(true);
    const response = await projectsApi.testConnection({
      host: formData.dbHost,
      port: formData.dbPort,
      database: formData.dbName,
      username: formData.dbUser,
      password: formData.dbPassword,
    });

    setIsTestingNewConnection(false);
    if (response.success) {
      setAlertInfo({ message: '数据库连接测试成功！', type: 'default' });
    } else {
      setAlertInfo({ message: `数据库连接测试失败：${response.error}`, type: 'destructive' });
    }
  };

  // 测试加密解密功能
  const handleTestCrypto = async () => {
    if (!formData.dbPassword) {
      setAlertInfo({ message: '请先输入密码', type: 'destructive' });
      return;
    }

    const response = await projectsApi.testCrypto(formData.dbPassword);
    if (response.success && response.data) {
      const { original, encrypted, decrypted, isMatch, encryptionKeyLength, isDefaultKey } = response.data;
      const message = `加密解密测试结果：
      
原始密码: ${original}
加密结果: ${encrypted}
解密结果: ${decrypted}
结果匹配: ${isMatch ? '✅ 是' : '❌ 否'}
加密密钥长度: ${encryptionKeyLength}
使用默认密钥: ${isDefaultKey ? '是（不安全）' : '否'}

请检查控制台查看详细调试信息`;
      setAlertInfo({ message, type: 'default' });
    } else {
      setAlertInfo({ message: `加密解密测试失败：${response.error}`, type: 'destructive' });
    }
  };

  // 获取权限设置指南
  const handleGetPermissionGuide = async (projectId: number) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/setup-permissions`);
      const result = await response.json();
      
      if (result.success && result.data) {
        // 创建权限设置指南对话框
        const { project, permissionSQL, connectionCommands, troubleshooting } = result.data;
        
        const guideContent = `
数据库权限设置指南
====================

项目信息：
- 项目名称: ${project.name}
- 数据库: ${project.dbName}
- 用户: ${project.dbUser}
- 主机: ${project.dbHost}:${project.dbPort}

连接命令：
${connectionCommands.psql}

推荐权限设置（复制并在数据库中执行）：
${permissionSQL.fullPermissions}

最小权限设置（仅创建表）：
${permissionSQL.minimalPermissions}

权限检查SQL：
${permissionSQL.checkPermissions}

常见问题：
${troubleshooting.commonIssues.map((issue: { issue: string; solution: string }) => `- ${issue.issue}: ${issue.solution}`).join('\n')}

设置步骤：
${troubleshooting.steps.map((step: string) => `${step}`).join('\n')}
        `;
        
        // 使用浏览器内置的复制功能
        await navigator.clipboard.writeText(guideContent);
        setAlertInfo({ message: '权限设置指南已复制到剪贴板！请在数据库管理工具中执行相应的SQL命令。', type: 'default' });
      } else {
        setAlertInfo({ message: `获取权限设置指南失败：${result.error}`, type: 'destructive' });
      }
    } catch (error) {
      console.error('获取权限设置指南失败:', error);
      setAlertInfo({ message: '获取权限设置指南失败，请检查网络连接', type: 'destructive' });
    }
  };

  // 测试已有项目的数据库连接
  const handleTestConnection = async (projectId: number) => {
    setTestingConnection(projectId);
    const response = await projectsApi.checkTables(projectId);
    setTestingConnection(null);
    
    if (response.success && response.data) {
      setAlertInfo({ message: `数据库连接测试成功！\n发现 ${response.data.totalTables} 个表`, type: 'default' });
    } else {
      setAlertInfo({ message: `数据库连接测试失败：${response.error}`, type: 'destructive' });
    }
  };

  // 查看表结构
  const handleViewTableStructure = async (projectId: number) => {
    setLoadingTableStructure(projectId);
    const response = await projectsApi.checkTables(projectId);
    setLoadingTableStructure(null);
    
    if (response.success && response.data) {
      setTableStructureData(response.data);
      setSelectedTablesToCreate([]); // 重置选择
      setIsTableStructureDialogOpen(true);
    } else {
      setAlertInfo({ message: `获取表结构失败：${response.error}`, type: 'destructive' });
    }
  };

  // 创建缺失的表
  const handleCreateTables = async () => {
    if (!tableStructureData || selectedTablesToCreate.length === 0) {
      return;
    }

    setCreatingTables(true);
    const response = await projectsApi.createTables(tableStructureData.projectId, selectedTablesToCreate);
    setCreatingTables(false);

    if (response.success && response.data) {
      const { summary, createdTables } = response.data;
      
      // 显示创建结果
      const successTables = createdTables.filter(t => t.success);
      const failedTables = createdTables.filter(t => !t.success);
      
      let message = `表创建完成！\n成功: ${summary.success} 个，失败: ${summary.failed} 个`;
      
      if (successTables.length > 0) {
        message += `\n\n成功创建的表:\n${successTables.map(t => `- ${t.displayName}`).join('\n')}`;
      }
      
      if (failedTables.length > 0) {
        message += `\n\n创建失败的表:\n${failedTables.map(t => `- ${t.displayName}: ${t.error}`).join('\n')}`;
        
        // 检查是否有权限相关错误
        const hasPermissionError = failedTables.some(t => t.error?.includes('权限'));
        if (hasPermissionError) {
          message += `\n\n💡 发现权限相关错误，请点击项目列表中的"权限设置"按钮获取详细的权限配置指南。`;
        }
      }
      
      setAlertInfo({ message, type: 'default' });
      
      // 重新获取表结构
      handleViewTableStructure(tableStructureData.projectId);
    } else {
      setAlertInfo({ message: `创建表结构失败：${response.error}`, type: 'destructive' });
    }
  };

  // 切换项目状态
  const handleToggleStatus = async (projectId: number) => {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;

    const response = await projectsApi.updateProject(projectId, {
      isActive: !project.isActive
    });

    if (response.success) {
      setProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, isActive: !p.isActive } : p
      ));
    } else {
      setAlertInfo({ message: response.error || '更新项目状态失败', type: 'destructive' });
    }
  };

  // 删除项目
  const handleDeleteProject = async (projectId: number) => {
    if (!confirm('确定要删除这个项目吗？此操作不可逆。')) {
      return;
    }

    const response = await projectsApi.deleteProject(projectId);
    if (response.success) {
      setProjects(prev => prev.filter(p => p.id !== projectId));
      setAlertInfo({ message: '项目删除成功', type: 'default' });
    } else {
      setAlertInfo({ message: response.error || '删除项目失败', type: 'destructive' });
    }
  };

  // 添加项目
  const handleAddProject = async () => {
    const response = await projectsApi.createProject({
      ...formData,
      config: {
        s3: formData.config?.s3 || { bucket: '', cdn: '' },
      },
    });

    if (response.success && response.data) {
      setProjects(prev => [...prev, response.data!]);
      setIsAddDialogOpen(false);
      resetForm();
      setAlertInfo({ message: '项目创建成功', type: 'default' });
    } else {
      setAlertInfo({ message: response.error || '创建项目失败', type: 'destructive' });
    }
  };

  // 编辑项目
  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    setFormData({
      name: project.name,
      description: project.description,
      domain: project.domain,
      logo: project.logo,
      dbHost: project.dbHost,
      dbPort: project.dbPort,
      dbName: project.dbName,
      dbUser: project.dbUser,
      dbPassword: '', // 不显示密码
      config: {
        s3: project.config.s3 || { bucket: '', cdn: '' },
      },
    });
    setIsEditDialogOpen(true);
  };

  // 更新项目
  const handleUpdateProject = async () => {
    if (!editingProject) return;

    const updateData: Partial<ProjectFormData> = {
      name: formData.name,
      description: formData.description,
      domain: formData.domain,
      logo: formData.logo,
      dbHost: formData.dbHost,
      dbPort: formData.dbPort,
      dbName: formData.dbName,
      dbUser: formData.dbUser,
      config: {
        s3: formData.config?.s3 || { bucket: '', cdn: '' },
      },
    };

    // 只有输入了密码才更新密码
    if (formData.dbPassword) {
      updateData.dbPassword = formData.dbPassword;
    }

    const response = await projectsApi.updateProject(editingProject.id, updateData);

    if (response.success && response.data) {
      setProjects(prev => prev.map(p => 
        p.id === editingProject.id ? response.data! : p
      ));
      setIsEditDialogOpen(false);
      setEditingProject(null);
      resetForm();
      setAlertInfo({ message: '项目更新成功', type: 'default' });
    } else {
      setAlertInfo({ message: response.error || '更新项目失败', type: 'destructive' });
    }
  };

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      domain: '',
      logo: '',
      dbHost: '',
      dbPort: 5432,
      dbName: '',
      dbUser: '',
      dbPassword: '',
      config: {  s3: { bucket: '', cdn: '' } },
    });
  };

  // 关闭对话框
  const handleCloseDialog = () => {
    setIsAddDialogOpen(false);
    setIsEditDialogOpen(false);
    setEditingProject(null);
    resetForm();
  };

  // 渲染项目表单
  const renderProjectForm = () => (
    <div className="grid gap-4 py-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">项目名称</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="输入项目名称"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="domain">域名</Label>
          <Input
            id="domain"
            value={formData.domain}
            onChange={(e) => handleInputChange('domain', e.target.value)}
            placeholder="example.com"
          />
        </div>
      </div>
      {/* logo上传 */}
      <div className="space-y-2">
        <ImageUpload
          value={formData.logo}
          onChange={(url) => handleInputChange('logo', url)}
          projectId={0}
          label="项目Logo"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="description">项目描述</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="简要描述项目用途和功能"
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="dbHost">数据库主机</Label>
          <Input
            id="dbHost"
            value={formData.dbHost}
            onChange={(e) => handleInputChange('dbHost', e.target.value)}
            placeholder="localhost 或 IP地址"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="dbPort">端口</Label>
          <Input
            id="dbPort"
            type="number"
            value={formData.dbPort}
            onChange={(e) => handleInputChange('dbPort', parseInt(e.target.value) || 5432)}
            placeholder="5432"
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="dbName">数据库名</Label>
          <Input
            id="dbName"
            value={formData.dbName}
            onChange={(e) => handleInputChange('dbName', e.target.value)}
            placeholder="database_name"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="dbUser">用户名</Label>
          <Input
            id="dbUser"
            value={formData.dbUser}
            onChange={(e) => handleInputChange('dbUser', e.target.value)}
            placeholder="数据库用户名"
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="dbPassword">
          密码 {editingProject && <span className="text-sm text-muted-foreground">(留空则不修改)</span>}
        </Label>
        <Input
          id="dbPassword"
          type="password"
          value={formData.dbPassword}
          onChange={(e) => handleInputChange('dbPassword', e.target.value)}
          placeholder="数据库密码"
        />
      </div>
      {/* s3信息输入 */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="s3-bucket">S3 Bucket</Label>
          <Input
            id="s3-bucket"
            value={formData.config?.s3?.bucket || ''}
            onChange={(e) => handleS3Change('bucket', e.target.value)}
            placeholder="S3存储桶名称"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="s3-cdn">S3 CDN域名</Label>
          <Input
            id="s3-cdn"
            value={formData.config?.s3?.cdn || ''}
            onChange={(e) => handleS3Change('cdn', e.target.value)}
            placeholder="CDN域名"
          />
        </div>
      </div>
      {/* 测试连接和加密解密按钮 */}
      <div className="flex justify-end gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={handleTestCrypto}
          disabled={!formData.dbPassword}
        >
          <TestTube className="mr-2 h-4 w-4" />
          测试加密解密
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={handleTestNewConnection}
          disabled={isTestingNewConnection}
        >
          {isTestingNewConnection ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              测试中...
            </>
          ) : (
            <>
              <TestTube className="mr-2 h-4 w-4" />
              测试连接
            </>
          )}
        </Button>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  return (
    <>
      {alertInfo && (
        <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-full max-w-md">
          <Alert variant={alertInfo.type || 'default'}>
            <AlertDescription>{alertInfo.message}</AlertDescription>
          </Alert>
        </div>
      )}
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">业务项目管理</h1>
          </div>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                添加项目
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>添加新项目</DialogTitle>
              </DialogHeader>
              {renderProjectForm()}
              <DialogFooter>
                <Button variant="outline" onClick={handleCloseDialog}>
                  取消
                </Button>
                <Button onClick={handleAddProject}>
                  添加项目
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* 编辑项目对话框 */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>编辑项目</DialogTitle>
                <DialogDescription>
                  修改项目信息和配置
                </DialogDescription>
              </DialogHeader>
              {renderProjectForm()}
              <DialogFooter>
                <Button variant="outline" onClick={handleCloseDialog}>
                  取消
                </Button>
                <Button onClick={handleUpdateProject}>
                  更新项目
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* 项目列表 */}
        <Card>
          <CardHeader>
            <CardTitle>项目列表</CardTitle>
            <CardDescription>
              当前管理的所有业务项目
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>项目信息</TableHead>
                  <TableHead>数据库配置</TableHead>
                  <TableHead>LOGO</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {projects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{project.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {project.domain}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {project.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-2">
                          <Database className="h-3 w-3" />
                          {project.dbHost}:{project.dbPort}
                        </div>
                        <div className="text-muted-foreground">
                          {project.dbName}
                        </div>
                      </div>
                    </TableCell>
                  <TableCell>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center gap-2">
                          {project.logo ? (
                            <img className="h-10 w-10 rounded-full" src={project.logo} alt={project.name} />
                          ) : (
                            <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                              <span className="text-gray-500">无</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={project.isActive ? "default" : "secondary"}>
                        {project.isActive ? "运行中" : "已停止"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTestConnection(project.id)}
                          disabled={testingConnection === project.id}
                        >
                          {testingConnection === project.id ? (
                            <>
                              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                              测试中
                            </>
                          ) : (
                            <>
                              <TestTube className="mr-1 h-3 w-3" />
                              测试连接
                            </>
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewTableStructure(project.id)}
                          disabled={loadingTableStructure === project.id}
                        >
                          {loadingTableStructure === project.id ? (
                            <>
                              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                              加载中
                            </>
                          ) : (
                            <>
                              <Eye className="mr-1 h-3 w-3" />
                              查看表结构
                            </>
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleGetPermissionGuide(project.id)}
                          title="获取数据库权限设置指南"
                        >
                          <FileText className="mr-1 h-3 w-3" />
                          权限设置
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleStatus(project.id)}
                        >
                          {project.isActive ? '停止' : '启动'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditProject(project)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteProject(project.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* 表结构查看对话框 */}
        <Dialog open={isTableStructureDialogOpen} onOpenChange={setIsTableStructureDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {tableStructureData?.projectName} - 数据库表结构
              </DialogTitle>
              <DialogDescription>
                数据库：{tableStructureData?.database} | 总表数：{tableStructureData?.totalTables}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {/* 缺失表提示 */}
              {tableStructureData?.needsSetup && tableStructureData?.missingTables && tableStructureData.missingTables.length > 0 && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardHeader>
                    <CardTitle className="text-orange-800 flex items-center gap-2">
                      <AlertCircle className="h-5 w-5" />
                      检测到缺失的表结构
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="grid gap-2">
                        {tableStructureData.missingTables.map((missingTable, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={`missing-${missingTable.name}`}
                              checked={selectedTablesToCreate.includes(missingTable.name)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSelectedTablesToCreate(prev => [...prev, missingTable.name]);
                                } else {
                                  setSelectedTablesToCreate(prev => prev.filter(name => name !== missingTable.name));
                                }
                              }}
                            />
                            <Label htmlFor={`missing-${missingTable.name}`} className="flex-1">
                              <div>
                                <div className="font-medium">{missingTable.displayName}</div>
                                <div className="text-sm text-muted-foreground">{missingTable.description}</div>
                              </div>
                            </Label>
                          </div>
                        ))}
                      </div>
                      <div className="flex gap-2 pt-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedTablesToCreate(tableStructureData?.missingTables?.map(t => t.name) || []);
                          }}
                        >
                          全选
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedTablesToCreate([])}
                        >
                          取消全选
                        </Button>
                        <Button
                          onClick={handleCreateTables}
                          disabled={selectedTablesToCreate.length === 0 || creatingTables}
                          className="ml-auto"
                        >
                          {creatingTables ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              创建中...
                            </>
                          ) : (
                            `创建选中的表 (${selectedTablesToCreate.length})`
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 现有表结构 */}
              {tableStructureData?.tables?.map((table, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      {table.name}
                    </CardTitle>
                    {table.comment && (
                      <CardDescription>{table.comment}</CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium mb-2">字段信息</h4>
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b">
                                <th className="text-left p-2">字段名</th>
                                <th className="text-left p-2">类型</th>
                                <th className="text-left p-2">可空</th>
                                <th className="text-left p-2">默认值</th>
                              </tr>
                            </thead>
                            <tbody>
                              {table.columns?.map((column, colIndex) => (
                                <tr key={colIndex} className="border-b">
                                  <td className="p-2 font-medium">{column.name}</td>
                                  <td className="p-2">{column.type}</td>
                                  <td className="p-2">{column.nullable ? '是' : '否'}</td>
                                  <td className="p-2">{column.default || '-'}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                      
                      {table.primaryKeys?.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2">主键</h4>
                          <div className="flex flex-wrap gap-1">
                            {table.primaryKeys.map((pk, pkIndex) => (
                              <Badge key={pkIndex} variant="default">{pk}</Badge>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {table.foreignKeys?.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2">外键</h4>
                          <div className="space-y-1">
                            {table.foreignKeys.map((fk, fkIndex) => (
                              <div key={fkIndex} className="text-sm">
                                <Badge variant="outline">{fk.column}</Badge> → {fk.referencedTable}.{fk.referencedColumn}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsTableStructureDialogOpen(false)}>
                关闭
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
} 