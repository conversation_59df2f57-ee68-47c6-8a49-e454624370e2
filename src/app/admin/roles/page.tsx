"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Plus,
  Shield,
  Edit,
  Trash2,
  Users,
  Lock
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

// 模拟数据 - 实际应用中从API获取
const mockRoles = [
  {
    id: 1,
    name: '超级管理员',
    description: '系统超级管理员，拥有所有权限',
    permissions: ['*'],
    isSystem: true,
    userCount: 1,
    createdAt: '2024-01-15',
  },
  {
    id: 2,
    name: '管理员',
    description: '系统管理员，可管理业务项目和用户',
    permissions: [
      'projects:read',
      'projects:create',
      'projects:update',
      'projects:delete',
      'users:read',
      'users:create',
      'users:update',
      'content:read',
      'content:create',
      'content:update',
      'content:delete',
    ],
    isSystem: true,
    userCount: 1,
    createdAt: '2024-01-15',
  },
  {
    id: 3,
    name: '操作员',
    description: '系统操作员，可查看和管理内容',
    permissions: [
      'projects:read',
      'users:read',
      'content:read',
      'content:create',
      'content:update',
    ],
    isSystem: true,
    userCount: 1,
    createdAt: '2024-01-15',
  },
];

// 所有可用权限
const allPermissions = [
  {
    category: '项目管理',
    permissions: [
      { key: 'projects:read', name: '查看项目', description: '查看业务项目列表和详情' },
      { key: 'projects:create', name: '创建项目', description: '添加新的业务项目' },
      { key: 'projects:update', name: '编辑项目', description: '修改项目配置和信息' },
      { key: 'projects:delete', name: '删除项目', description: '删除业务项目' },
    ]
  },
  {
    category: '用户管理',
    permissions: [
      { key: 'users:read', name: '查看用户', description: '查看用户列表和信息' },
      { key: 'users:create', name: '创建用户', description: '添加新用户账号' },
      { key: 'users:update', name: '编辑用户', description: '修改用户信息和状态' },
      { key: 'users:delete', name: '删除用户', description: '删除用户账号' },
    ]
  },
  {
    category: '角色权限',
    permissions: [
      { key: 'roles:read', name: '查看角色', description: '查看角色列表和权限' },
      { key: 'roles:create', name: '创建角色', description: '添加新的角色' },
      { key: 'roles:update', name: '编辑角色', description: '修改角色权限配置' },
      { key: 'roles:delete', name: '删除角色', description: '删除角色' },
    ]
  },
  {
    category: '内容管理',
    permissions: [
      { key: 'content:read', name: '查看内容', description: '查看内容数据' },
      { key: 'content:create', name: '创建内容', description: '添加新内容' },
      { key: 'content:update', name: '编辑内容', description: '修改内容信息' },
      { key: 'content:delete', name: '删除内容', description: '删除内容数据' },
    ]
  },
  {
    category: '系统设置',
    permissions: [
      { key: 'system:read', name: '查看设置', description: '查看系统配置' },
      { key: 'system:update', name: '修改设置', description: '修改系统配置' },
      { key: 'config:read', name: '查看配置', description: '查看站点配置' },
      { key: 'config:update', name: '修改配置', description: '修改站点配置' },
    ]
  },
];

interface RoleFormData {
  name: string;
  description: string;
  permissions: string[];
}

export default function RolesPage() {
  const [roles, setRoles] = useState(mockRoles);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [formData, setFormData] = useState<RoleFormData>({
    name: '',
    description: '',
    permissions: [],
  });
  const [alertInfo, setAlertInfo] = useState<{ message: string; type?: 'default' | 'destructive' } | null>(null);

  const handleInputChange = (field: keyof RoleFormData, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePermissionToggle = (permission: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const handleAddRole = () => {
    // 这里应该调用API添加角色
    const newRole = {
      id: Date.now(),
      name: formData.name,
      description: formData.description,
      permissions: formData.permissions,
      isSystem: false,
      userCount: 0,
      createdAt: new Date().toISOString().split('T')[0],
    };
    
    setRoles(prev => [...prev, newRole]);
    setIsAddDialogOpen(false);
    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      permissions: [],
    });
  };

  const handleDeleteRole = (roleId: number) => {
    const role = roles.find(r => r.id === roleId);
    if (role?.isSystem) {
      setAlertInfo({ message: '系统内置角色不能删除！', type: 'destructive' });
      return;
    }
    if (confirm('确定要删除这个角色吗？此操作不可逆。')) {
      setRoles(prev => prev.filter(role => role.id !== roleId));
    }
  };

  const hasPermission = (rolePermissions: string[], permission: string) => {
    return rolePermissions.includes('*') || rolePermissions.includes(permission);
  };

  return (
    <>
      {alertInfo && (
        <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-full max-w-md">
          <Alert variant={alertInfo.type || 'default'}>
            <AlertDescription>{alertInfo.message}</AlertDescription>
          </Alert>
        </div>
      )}
      <div className="space-y-6">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">角色权限管理</h1>
            <p className="text-muted-foreground">
              管理系统角色和权限配置，控制用户访问范围
            </p>
          </div>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                添加角色
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>添加新角色</DialogTitle>
                <DialogDescription>
                  创建一个新的系统角色并配置权限
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">角色名称</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="输入角色名称"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">角色描述</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="描述角色的用途和职责"
                  />
                </div>

                <div className="space-y-4">
                  <Label>权限配置</Label>
                  {allPermissions.map((category) => (
                    <Card key={category.category}>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">{category.category}</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        {category.permissions.map((permission) => (
                          <div key={permission.key} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id={permission.key}
                              checked={formData.permissions.includes(permission.key)}
                              onChange={() => handlePermissionToggle(permission.key)}
                            />
                            <div className="flex-1">
                              <Label htmlFor={permission.key} className="text-sm font-medium">
                                {permission.name}
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                {permission.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  取消
                </Button>
                <Button onClick={handleAddRole}>
                  创建角色
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* 角色统计卡片 */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总角色数</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{roles.length}</div>
              <p className="text-xs text-muted-foreground">
                系统中的所有角色
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">系统角色</CardTitle>
              <Lock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{roles.filter(r => r.isSystem).length}</div>
              <p className="text-xs text-muted-foreground">
                系统内置角色数量
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">自定义角色</CardTitle>
              <Edit className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{roles.filter(r => !r.isSystem).length}</div>
              <p className="text-xs text-muted-foreground">
                用户创建的角色
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已分配用户</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {roles.reduce((sum, role) => sum + role.userCount, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                拥有角色的用户总数
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 角色列表 */}
        <Card>
          <CardHeader>
            <CardTitle>角色列表</CardTitle>
            <CardDescription>
              系统中所有角色的详细信息和权限配置
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>角色信息</TableHead>
                  <TableHead>权限摘要</TableHead>
                  <TableHead>用户数量</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium flex items-center gap-2">
                          <Shield className="h-4 w-4" />
                          {role.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {role.description}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          创建于 {role.createdAt}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-2">
                        {role.permissions.includes('*') ? (
                          <Badge variant="default">全部权限</Badge>
                        ) : (
                          <div className="flex flex-wrap gap-1">
                            {allPermissions.slice(0, 2).map((category) => {
                              const hasAnyPermission = category.permissions.some(p => 
                                hasPermission(role.permissions, p.key)
                              );
                              return hasAnyPermission ? (
                                <Badge key={category.category} variant="secondary" className="text-xs">
                                  {category.category}
                                </Badge>
                              ) : null;
                            })}
                            {role.permissions.length > 4 && (
                              <Badge variant="outline" className="text-xs">
                                +{role.permissions.length - 4} 更多
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{role.userCount} 个用户</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={role.isSystem ? "default" : "secondary"}>
                        {role.isSystem ? "系统角色" : "自定义"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => console.log('编辑角色:', role.name)}
                          disabled={role.isSystem}
                        >
                          <Edit className="h-3 w-3" />
                          编辑
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteRole(role.id)}
                          disabled={role.isSystem || role.userCount > 0}
                        >
                          <Trash2 className="h-3 w-3" />
                          删除
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </>
  );
} 