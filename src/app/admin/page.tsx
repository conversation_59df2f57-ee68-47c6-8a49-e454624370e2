import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  Users, 
  Globe, 
  Activity,
  ArrowUpIcon,
  ArrowDownIcon,
  TrendingUp
} from 'lucide-react';

// 模拟数据 - 实际应用中从数据库获取
const stats = [
  {
    title: '业务项目',
    value: '12',
    change: '+2',
    changeType: 'increase' as const,
    description: '本月新增 2 个项目',
    icon: Database,
  },
  {
    title: '系统用户',
    value: '48',
    change: '+8',
    changeType: 'increase' as const,
    description: '本月新增 8 个用户',
    icon: Users,
  },
  {
    title: '活跃项目',
    value: '9',
    change: '-1',
    changeType: 'decrease' as const,
    description: '1 个项目暂停服务',
    icon: Globe,
  },
  {
    title: '总访问量',
    value: '25.4k',
    change: '+12%',
    changeType: 'increase' as const,
    description: '相比上月增长 12%',
    icon: Activity,
  },
];

const recentProjects = [
  {
    id: 1,
    name: '企业官网',
    domain: 'company.com',
    status: 'active',
    lastActivity: '2小时前',
    dbSize: '2.4GB',
  },
  {
    id: 2,
    name: '电商平台',
    domain: 'shop.example.com',
    status: 'active',
    lastActivity: '5分钟前',
    dbSize: '8.7GB',
  },
  {
    id: 3,
    name: '博客系统',
    domain: 'blog.site.com',
    status: 'maintenance',
    lastActivity: '1天前',
    dbSize: '1.2GB',
  },
  {
    id: 4,
    name: '在线文档',
    domain: 'docs.platform.io',
    status: 'active',
    lastActivity: '30分钟前',
    dbSize: '945MB',
  },
];

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">系统概览</h1>
        <p className="text-muted-foreground">
          欢迎使用多业务统一管理平台，这里是系统的整体概况
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {stat.changeType === 'increase' ? (
                  <ArrowUpIcon className="mr-1 h-3 w-3 text-green-500" />
                ) : (
                  <ArrowDownIcon className="mr-1 h-3 w-3 text-red-500" />
                )}
                <span className={stat.changeType === 'increase' ? 'text-green-500' : 'text-red-500'}>
                  {stat.change}
                </span>
                <span className="ml-1">{stat.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 最近活动的项目 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              最近活跃项目
            </CardTitle>
            <CardDescription>
              显示最近有活动的业务项目
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentProjects.map((project) => (
                <div key={project.id} className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {project.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {project.domain}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={project.status === 'active' ? 'default' : 'secondary'}
                    >
                      {project.status === 'active' ? '运行中' : '维护中'}
                    </Badge>
                    <div className="text-right">
                      <p className="text-xs text-muted-foreground">
                        {project.lastActivity}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {project.dbSize}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 系统状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              系统状态
            </CardTitle>
            <CardDescription>
              关键系统指标和健康状况
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">主数据库连接</span>
                <Badge variant="default">正常</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">业务数据库连接</span>
                <Badge variant="default">9/12 正常</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">存储空间使用</span>
                <span className="text-sm text-muted-foreground">45.2GB / 100GB</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">内存使用率</span>
                <span className="text-sm text-muted-foreground">68%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">CPU 使用率</span>
                <span className="text-sm text-muted-foreground">23%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
          <CardDescription>
            常用的管理操作和快捷入口
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <Database className="h-8 w-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">添加项目</h3>
                    <p className="text-sm text-muted-foreground">
                      接入新的业务项目
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <Users className="h-8 w-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">用户管理</h3>
                    <p className="text-sm text-muted-foreground">
                      管理系统用户权限
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <Globe className="h-8 w-8 text-primary" />
                  <div>
                    <h3 className="font-semibold">内容管理</h3>
                    <p className="text-sm text-muted-foreground">
                      管理站点内容和配置
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 