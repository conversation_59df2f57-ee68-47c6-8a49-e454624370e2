import { NextRequest, NextResponse } from 'next/server';
import { mainDb, businessProjects } from '@/lib/db';
import { encrypt } from '@/lib/crypto';
import { eq } from 'drizzle-orm';

// 获取项目列表
export async function GET() {
  try {
    const projects = await mainDb
      .select({
        id: businessProjects.id,
        name: businessProjects.name,
        description: businessProjects.description,
        domain: businessProjects.domain,
        dbHost: businessProjects.dbHost,
        dbPort: businessProjects.dbPort,
        dbName: businessProjects.dbName,
        dbUser: businessProjects.dbUser,
        isActive: businessProjects.isActive,
        config: businessProjects.config,
        logo: businessProjects.logo,
        createdAt: businessProjects.createdAt,
        updatedAt: businessProjects.updatedAt,
      })
      .from(businessProjects)
      .orderBy(businessProjects.createdAt);

    // 解密数据库密码（在返回给前端时不包含密码）
    const projectsWithoutPassword = projects.map(project => ({
      ...project,
      // 不返回密码字段
    }));

    return NextResponse.json({ 
      success: true, 
      data: projectsWithoutPassword 
    });
  } catch (error) {
    console.error('获取项目列表失败:', error);
    return NextResponse.json(
      { success: false, error: '获取项目列表失败' },
      { status: 500 }
    );
  }
}

// 创建新项目
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      domain,
      dbHost,
      dbPort,
      dbName,
      dbUser,
      dbPassword,
      config,
      logo,
    } = body;

    // 验证必填字段
    if (!name || !dbHost || !dbName || !dbUser || !dbPassword) {
      return NextResponse.json(
        { success: false, error: '请填写所有必填字段' },
        { status: 400 }
      );
    }

    // 加密数据库密码
    const encryptedPassword = await encrypt(dbPassword);

    // 插入新项目
    const [newProject] = await mainDb
      .insert(businessProjects)
      .values({
        name,
        description,
        domain,
        dbHost,
        dbPort: dbPort || 5432,
        dbName,
        dbUser,
        dbPassword: encryptedPassword,
        config: config || { s3: { bucket: '', cdn: '' } },
        isActive: true,
        logo: logo || '',
      })
      .returning({
        id: businessProjects.id,
        name: businessProjects.name,
        description: businessProjects.description,
        domain: businessProjects.domain,
        dbHost: businessProjects.dbHost,
        dbPort: businessProjects.dbPort,
        dbName: businessProjects.dbName,
        dbUser: businessProjects.dbUser,
        isActive: businessProjects.isActive,
        config: businessProjects.config,
        createdAt: businessProjects.createdAt,
      });

    return NextResponse.json({ 
      success: true, 
      data: newProject 
    });
  } catch (error) {
    console.error('创建项目失败:', error);
    return NextResponse.json(
      { success: false, error: '创建项目失败' },
      { status: 500 }
    );
  }
}

// 更新项目
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      id,
      name,
      description,
      domain,
      dbHost,
      dbPort,
      dbName,
      dbUser,
      dbPassword,
      config,
      isActive,
      logo,
    } = body;

    // 验证必填字段
    if (!id || !name || !dbHost || !dbName || !dbUser) {
      return NextResponse.json(
        { success: false, error: '请填写所有必填字段' },
        { status: 400 }
      );
    }

    // 准备更新数据
    const updateData: Partial<typeof businessProjects.$inferInsert> = {
      name,
      description,
      domain,
      dbHost,
      dbPort: dbPort || 5432,
      dbName,
      dbUser,
      config: config || { s3: { bucket: '', cdn: '' } },
      isActive: isActive !== undefined ? isActive : true,
      updatedAt: new Date(),
      logo: logo || '',
    };

    // 如果提供了新密码，则加密后更新
    if (dbPassword) {
      updateData.dbPassword = await encrypt(dbPassword);
    }

    // 更新项目
    const [updatedProject] = await mainDb
      .update(businessProjects)
      .set(updateData)
      .where(eq(businessProjects.id, id))
      .returning({
        id: businessProjects.id,
        name: businessProjects.name,
        description: businessProjects.description,
        domain: businessProjects.domain,
        dbHost: businessProjects.dbHost,
        dbPort: businessProjects.dbPort,
        dbName: businessProjects.dbName,
        dbUser: businessProjects.dbUser,
        isActive: businessProjects.isActive,
        config: businessProjects.config,
        createdAt: businessProjects.createdAt,
        updatedAt: businessProjects.updatedAt,
        logo: businessProjects.logo,
      });

    if (!updatedProject) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      data: updatedProject 
    });
  } catch (error) {
    console.error('更新项目失败:', error);
    return NextResponse.json(
      { success: false, error: '更新项目失败' },
      { status: 500 }
    );
  }
}

// 删除项目
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: '缺少项目ID' },
        { status: 400 }
      );
    }

    // 删除项目
    const [deletedProject] = await mainDb
      .delete(businessProjects)
      .where(eq(businessProjects.id, parseInt(id)))
      .returning({
        id: businessProjects.id,
        name: businessProjects.name,
      });

    if (!deletedProject) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      data: deletedProject 
    });
  } catch (error) {
    console.error('删除项目失败:', error);
    return NextResponse.json(
      { success: false, error: '删除项目失败' },
      { status: 500 }
    );
  }
} 