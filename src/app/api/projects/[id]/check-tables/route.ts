import { NextRequest, NextResponse } from 'next/server';
import { getRequiredTablesForFeatures, businessTableDescriptions } from '@/lib/db/business-schema-ddl';
import { getDatabaseConnection } from '@/lib/db-manager';

// 检查business数据库表结构
export async function GET(
  request: NextRequest,
) {
  try {
    const url = new URL(request.url);
    const projectId = parseInt(url.pathname.split('/').pop() || '0');
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { success: false, error: '无效的项目ID' },
        { status: 400 }
      );
    }

    // 获取项目信息
 
    const {queryClient: connection,project} = await getDatabaseConnection(projectId);

    try {
      // 查询数据库中的所有表
      const tablesQuery = await connection`
        SELECT 
          table_name,
          table_type
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
          AND table_type = 'BASE TABLE'
        ORDER BY table_name
      `;

      const tables = [];
      
      for (const table of tablesQuery) {
        // 查询每个表的列信息
        const columnsQuery = await connection`
          SELECT 
            column_name,
            data_type,
            is_nullable,
            column_default,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            ordinal_position
          FROM information_schema.columns 
          WHERE table_schema = 'public' 
            AND table_name = ${table.table_name}
          ORDER BY ordinal_position
        `;

        // 查询主键信息
        const primaryKeysQuery = await connection`
          SELECT column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
          WHERE tc.table_schema = 'public'
            AND tc.table_name = ${table.table_name}
            AND tc.constraint_type = 'PRIMARY KEY'
        `;

        // 查询外键信息
        const foreignKeysQuery = await connection`
          SELECT 
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
          JOIN information_schema.constraint_column_usage ccu
            ON ccu.constraint_name = tc.constraint_name
          WHERE tc.table_schema = 'public'
            AND tc.table_name = ${table.table_name}
            AND tc.constraint_type = 'FOREIGN KEY'
        `;

        // 查询索引信息
        const indexesQuery = await connection`
          SELECT 
            indexname,
            indexdef
          FROM pg_indexes
          WHERE schemaname = 'public'
            AND tablename = ${table.table_name}
        `;

        tables.push({
          name: table.table_name,
          type: table.table_type,
          comment: table.table_comment,
          columns: columnsQuery.map(col => ({
            name: col.column_name,
            type: col.data_type,
            nullable: col.is_nullable === 'YES',
            default: col.column_default,
            maxLength: col.character_maximum_length,
            precision: col.numeric_precision,
            scale: col.numeric_scale,
            position: col.ordinal_position,
          })),
          primaryKeys: primaryKeysQuery.map(pk => pk.column_name),
          foreignKeys: foreignKeysQuery.map(fk => ({
            column: fk.column_name,
            referencedTable: fk.foreign_table_name,
            referencedColumn: fk.foreign_column_name,
          })),
          indexes: indexesQuery.map(idx => ({
            name: idx.indexname,
            definition: idx.indexdef,
          })),
        });
      }

      await connection.end();

      const requiredTables = getRequiredTablesForFeatures();
      
      // 检查哪些表存在，哪些缺失
      const existingTableNames = tables.map(table => table.name);
      const missingTables = requiredTables.filter(tableName => !existingTableNames.includes(tableName));
      const extraTables = existingTableNames.filter(tableName => !requiredTables.includes(tableName));

      // 为缺失的表添加描述信息
      const missingTablesWithInfo = missingTables.map(tableName => ({
        name: tableName,
        displayName: businessTableDescriptions[tableName as keyof typeof businessTableDescriptions]?.name || tableName,
        description: businessTableDescriptions[tableName as keyof typeof businessTableDescriptions]?.description || '',
        features: businessTableDescriptions[tableName as keyof typeof businessTableDescriptions]?.features || []
      }));

      return NextResponse.json({
        success: true,
        data: {
          projectId: project.id,
          projectName: project.name,
          database: project.dbName,
          tables: tables,
          totalTables: tables.length,
          requiredTables,
          missingTables: missingTablesWithInfo,
          extraTables,
          needsSetup: missingTables.length > 0,
        }
      });
    } catch (dbError) {
      await connection.end();
      throw dbError;
    }
  } catch (error) {
    console.error('检查表结构失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '检查表结构失败' 
      },
      { status: 500 }
    );
  }
} 