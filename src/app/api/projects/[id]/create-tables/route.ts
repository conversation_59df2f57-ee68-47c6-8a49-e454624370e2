import { NextRequest, NextResponse } from 'next/server';
import { getRequiredTablesForFeatures, getTableDDL, businessTableDescriptions } from '@/lib/db/business-schema-ddl';
import { getDatabaseConnection } from '@/lib/db-manager';

// 创建缺失的数据库表
export async function POST(
  request: NextRequest,
) {
  try {
    const url = new URL(request.url);
    const projectId = parseInt(url.pathname.split('/').pop() || '0');
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { success: false, error: '无效的项目ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { tablesToCreate } = body;

    if (!Array.isArray(tablesToCreate) || tablesToCreate.length === 0) {
      return NextResponse.json(
        { success: false, error: '请指定要创建的表' },
        { status: 400 }
      );
    }

    // 获取项目信息
    const {queryClient: connection, project} = await getDatabaseConnection(projectId);

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      );
    }

    // 验证要创建的表是否都是有效的
    const requiredTables = getRequiredTablesForFeatures();
    const invalidTables = tablesToCreate.filter(tableName => !requiredTables.includes(tableName));
    if (invalidTables.length > 0) {
      return NextResponse.json(
        { success: false, error: `无效的表名: ${invalidTables.join(', ')}` },
        { status: 400 }
      );
    }

    const createdTables: Array<{
      name: string;
      displayName: string;
      success: boolean;
      error?: string;
    }> = [];

    try {
      // 首先检查数据库权限
      console.log('检查数据库权限...');
      try {
        // 检查是否有创建表的权限
        const permissionCheck = await connection`
          SELECT 
            has_schema_privilege(current_user, 'public', 'CREATE') as can_create_in_public,
            has_database_privilege(current_database(), 'CREATE') as can_create_in_db,
            current_user as db_user,
            current_database() as db_name
        `;
        
        console.log('权限检查结果:', permissionCheck[0]);
        
        if (!permissionCheck[0].can_create_in_public) {
          return NextResponse.json({
            success: false,
            error: `数据库用户 '${permissionCheck[0].db_user}' 没有在 'public' schema 中创建表的权限。请联系数据库管理员授予以下权限：
            
GRANT CREATE ON SCHEMA public TO ${permissionCheck[0].db_user};
GRANT USAGE ON SCHEMA public TO ${permissionCheck[0].db_user};

或者运行以下命令给用户完整权限：
GRANT ALL PRIVILEGES ON DATABASE ${permissionCheck[0].db_name} TO ${permissionCheck[0].db_user};
GRANT ALL PRIVILEGES ON SCHEMA public TO ${permissionCheck[0].db_user};`
          }, { status: 403 });
        }
      } catch (permError) {
        console.error('权限检查失败:', permError);
        return NextResponse.json({
          success: false,
          error: `无法检查数据库权限: ${permError instanceof Error ? permError.message : '未知错误'}`
        }, { status: 500 });
      }

      // 按照依赖关系顺序创建表
      const tableCreationOrder = [
        'page_meta',
        'blog_categories', 
        'blog_tags',
        'site_config',
        'page_stats',
        'blog_posts', // 依赖 blog_categories
        'blog_post_tags' // 依赖 blog_posts 和 blog_tags
      ];

      // 按顺序创建表
      for (const tableName of tableCreationOrder) {
        if (tablesToCreate.includes(tableName)) {
          try {
            console.log(`开始创建表: ${tableName}`);
            
            // 先检查表是否已存在
            const tableExists = await connection`
              SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = ${tableName}
              ) as exists
            `;
            
            if (tableExists[0].exists) {
              console.log(`表 ${tableName} 已存在，跳过创建`);
              createdTables.push({
                name: tableName,
                displayName: businessTableDescriptions[tableName as keyof typeof businessTableDescriptions]?.name || tableName,
                success: true
              });
              continue;
            }

            const ddl = getTableDDL(tableName);
            if (!ddl) {
              createdTables.push({
                name: tableName,
                displayName: businessTableDescriptions[tableName as keyof typeof businessTableDescriptions]?.name || tableName,
                success: false,
                error: '找不到表结构定义'
              });
              continue;
            }

            // 执行DDL创建表
            console.log(`执行DDL: ${ddl.substring(0, 100)}...`);
            await connection.unsafe(ddl.trim());
            
            console.log(`表 ${tableName} 创建成功`);
            createdTables.push({
              name: tableName,
              displayName: businessTableDescriptions[tableName as keyof typeof businessTableDescriptions]?.name || tableName,
              success: true
            });
          } catch (error) {
            console.error(`创建表 ${tableName} 失败:`, error);
            
            // 解析PostgreSQL错误
            let errorMessage = '创建失败';
            if (error instanceof Error) {
              if (error.message.includes('permission denied')) {
                errorMessage = '权限不足，无法创建表';
              } else if (error.message.includes('already exists')) {
                errorMessage = '表已存在';
              } else if (error.message.includes('relation') && error.message.includes('does not exist')) {
                errorMessage = '依赖的表不存在，请先创建相关表';
              } else {
                errorMessage = error.message;
              }
            }
            
            createdTables.push({
              name: tableName,
              displayName: businessTableDescriptions[tableName as keyof typeof businessTableDescriptions]?.name || tableName,
              success: false,
              error: errorMessage
            });
          }
        }
      }

      await connection.end();

      // 统计创建结果
      const successCount = createdTables.filter(t => t.success).length;
      const failCount = createdTables.filter(t => !t.success).length;

      return NextResponse.json({
        success: true,
        data: {
          projectId: project.id,
          projectName: project.name,
          database: project.dbName,
          createdTables,
          summary: {
            total: createdTables.length,
            success: successCount,
            failed: failCount
          }
        }
      });
    } catch (dbError) {
      await connection.end();
      throw dbError;
    }
  } catch (error) {
    console.error('创建表结构失败:', error);
    
    // 特殊处理权限错误
    let errorMessage = '创建表结构失败';
    if (error instanceof Error) {
      if (error.message.includes('permission denied')) {
        errorMessage = '数据库权限不足，请检查用户权限配置';
      } else if (error.message.includes('authentication failed')) {
        errorMessage = '数据库认证失败，请检查用户名和密码';
      } else if (error.message.includes('database') && error.message.includes('does not exist')) {
        errorMessage = '数据库不存在，请先创建数据库';
      } else {
        errorMessage = error.message;
      }
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage
      },
      { status: 500 }
    );
  }
} 