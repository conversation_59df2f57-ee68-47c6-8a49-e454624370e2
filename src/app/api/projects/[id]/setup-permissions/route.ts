import { NextRequest, NextResponse } from 'next/server';
import { getDatabaseConnection } from '@/lib/db-manager';

// 生成数据库权限设置的SQL命令
export async function GET(
  request: NextRequest,
) {
  try {
    const url = new URL(request.url);
    const projectId = parseInt(url.pathname.split('/').pop() || '0');
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { success: false, error: '无效的项目ID' },
        { status: 400 }
      );
    }

    // 获取项目信息
    const { project } = await getDatabaseConnection(projectId);

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      );
    }

    // 生成权限设置SQL
    const permissionSQL = {
      // 基本权限设置
      basicPermissions: [
        `-- 为用户 ${project.dbUser} 设置基本权限`,
        `-- 请以超级用户身份（如 postgres）连接到数据库 ${project.dbName} 执行以下命令：`,
        ``,
        `-- 1. 授予数据库级别权限`,
        `GRANT CONNECT ON DATABASE ${project.dbName} TO ${project.dbUser};`,
        `GRANT CREATE ON DATABASE ${project.dbName} TO ${project.dbUser};`,
        ``,
        `-- 2. 授予 public schema 权限`,
        `GRANT USAGE ON SCHEMA public TO ${project.dbUser};`,
        `GRANT CREATE ON SCHEMA public TO ${project.dbUser};`,
        ``,
        `-- 3. 授予对现有表的权限（如果有的话）`,
        `GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${project.dbUser};`,
        `GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${project.dbUser};`,
        ``,
        `-- 4. 设置默认权限（对未来创建的对象）`,
        `ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO ${project.dbUser};`,
        `ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO ${project.dbUser};`,
      ].join('\n'),

      // 最小权限设置（仅创建表）
      minimalPermissions: [
        `-- 最小权限设置（仅创建业务表）`,
        `-- 请以超级用户身份连接到数据库执行：`,
        ``,
        `GRANT USAGE ON SCHEMA public TO ${project.dbUser};`,
        `GRANT CREATE ON SCHEMA public TO ${project.dbUser};`,
      ].join('\n'),

      // 完整权限设置（推荐）
      fullPermissions: [
        `-- 完整权限设置（推荐用于开发环境）`,
        `-- 请以超级用户身份连接到数据库执行：`,
        ``,
        `-- 方案1：授予用户数据库owner权限（最简单）`,
        `ALTER DATABASE ${project.dbName} OWNER TO ${project.dbUser};`,
        ``,
        `-- 方案2：或者授予详细权限`,
        `GRANT ALL PRIVILEGES ON DATABASE ${project.dbName} TO ${project.dbUser};`,
        `GRANT ALL PRIVILEGES ON SCHEMA public TO ${project.dbUser};`,
        `GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${project.dbUser};`,
        `GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${project.dbUser};`,
        `ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO ${project.dbUser};`,
        `ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO ${project.dbUser};`,
      ].join('\n'),

      // 检查权限的SQL
      checkPermissions: [
        `-- 检查当前权限状态`,
        `-- 以用户 ${project.dbUser} 身份连接数据库后执行：`,
        ``,
        `SELECT `,
        `  current_user as current_db_user,`,
        `  current_database() as current_db,`,
        `  has_database_privilege(current_database(), 'CREATE') as can_create_in_db,`,
        `  has_schema_privilege('public', 'USAGE') as can_use_public_schema,`,
        `  has_schema_privilege('public', 'CREATE') as can_create_in_public_schema;`,
      ].join('\n'),
    };

    // 生成连接命令
    const connectionCommands = {
      psql: `psql -h ${project.dbHost} -p ${project.dbPort} -d ${project.dbName} -U postgres`,
      superuserConnection: `-- 1. 首先以超级用户连接（通常是 postgres 用户）`,
      userConnection: `-- 2. 然后以业务用户连接测试权限\npsql -h ${project.dbHost} -p ${project.dbPort} -d ${project.dbName} -U ${project.dbUser}`,
    };

    return NextResponse.json({
      success: true,
      data: {
        project: {
          id: project.id,
          name: project.name,
          dbName: project.dbName,
          dbUser: project.dbUser,
          dbHost: project.dbHost,
          dbPort: project.dbPort,
        },
        permissionSQL,
        connectionCommands,
        troubleshooting: {
          commonIssues: [
            {
              issue: "permission denied for schema public",
              solution: "用户没有在 public schema 中创建表的权限，需要执行基本权限设置"
            },
            {
              issue: "database does not exist",
              solution: "数据库不存在，需要先创建数据库：CREATE DATABASE " + project.dbName + ";"
            },
            {
              issue: "role does not exist",
              solution: "数据库用户不存在，需要先创建用户：CREATE USER " + project.dbUser + " WITH PASSWORD 'your_password';"
            },
            {
              issue: "authentication failed",
              solution: "用户名或密码错误，检查连接配置"
            }
          ],
          steps: [
            "1. 确认数据库服务正在运行",
            "2. 确认数据库和用户已创建",
            "3. 以超级用户身份执行权限设置SQL",
            "4. 以业务用户身份测试连接和权限",
            "5. 重新尝试创建表结构"
          ]
        }
      }
    });
  } catch (error) {
    console.error('生成权限设置失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '生成权限设置失败' 
      },
      { status: 500 }
    );
  }
} 