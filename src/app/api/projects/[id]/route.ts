import { NextRequest, NextResponse } from 'next/server';
import { mainDb, businessProjects } from '@/lib/db';
import { eq } from 'drizzle-orm';

// 根据ID获取单个项目
export async function GET(
  request: NextRequest,
) {
  try {
    const url = new URL(request.url);
    const projectId = parseInt(url.pathname.split('/').pop() || '0');
    
    if (isNaN(projectId)) {
      return NextResponse.json(
        { success: false, error: '无效的项目ID' },
        { status: 400 }
      );
    }

    const [project] = await mainDb
      .select({
        id: businessProjects.id,
        name: businessProjects.name,
        description: businessProjects.description,
        domain: businessProjects.domain,
        dbHost: businessProjects.dbHost,
        dbPort: businessProjects.dbPort,
        dbName: businessProjects.dbName,
        dbUser: businessProjects.dbUser,
        isActive: businessProjects.isActive,
        config: businessProjects.config,
        createdAt: businessProjects.createdAt,
        updatedAt: businessProjects.updatedAt,
      })
      .from(businessProjects)
      .where(eq(businessProjects.id, projectId))
      .limit(1);

    if (!project) {
      return NextResponse.json(
        { success: false, error: '项目不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({ 
      success: true, 
      data: project 
    });
  } catch (error) {
    console.error('获取项目失败:', error);
    return NextResponse.json(
      { success: false, error: '获取项目失败' },
      { status: 500 }
    );
  }
} 