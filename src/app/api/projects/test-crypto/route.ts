import { NextRequest, NextResponse } from 'next/server';
import { encrypt, decrypt } from '@/lib/crypto';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { password } = body;

    if (!password) {
      return NextResponse.json(
        { success: false, error: '请提供密码' },
        { status: 400 }
      );
    }
    
    // 加密
    const encrypted = await encrypt(password);
    
    // 解密
    const decrypted = await decrypt(encrypted);
    
    // 验证是否一致
    const isMatch = password === decrypted;

    const keyString = process.env.ENCRYPTION_KEY || 'default-32-char-secret-key-here!!';

    return NextResponse.json({
      success: true,
      data: {
        original: password,
        encrypted: encrypted,
        decrypted: decrypted,
        isMatch: isMatch,
        encryptionKeyLength: keyString.length,
        isDefaultKey: keyString === 'default-32-char-secret-key-here!!',
      }
    });
  } catch (error) {
    console.error('加密解密测试失败:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '测试失败',
        details: error 
      },
      { status: 500 }
    );
  }
} 