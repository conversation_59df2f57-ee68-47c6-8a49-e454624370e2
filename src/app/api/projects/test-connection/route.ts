import { NextRequest, NextResponse } from 'next/server';
import { testDatabaseConnection } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { host, port, database, username, password } = body;

    // 验证必填字段
    if (!host || !database || !username || !password) {
      return NextResponse.json(
        { success: false, error: '请填写所有数据库连接信息' },
        { status: 400 }
      );
    }

    // 测试数据库连接
    const result = await testDatabaseConnection({
      host,
      port: port || 5432,
      database,
      username,
      password,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('测试数据库连接失败:', error);
    return NextResponse.json({
      success: false,
      error: '测试连接时发生错误'
    }, { status: 500 });
  }
} 