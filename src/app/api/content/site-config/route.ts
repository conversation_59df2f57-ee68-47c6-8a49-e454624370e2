import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db-manager';
import { siteConfig } from '@/lib/db/schema/business';
import { eq, inArray } from 'drizzle-orm';

// 获取站点配置
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const keys = searchParams.get('keys')?.split(',') || [];

    if (!projectId) {
      return NextResponse.json({
        success: false,
        error: '缺少项目ID参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const configs = await executeQuery(projectId, async (businessDb) => {
      if (keys.length > 0) {
        return await businessDb
          .select()
          .from(siteConfig)
          .where(inArray(siteConfig.key, keys));
      } else {
        return await businessDb
          .select()
          .from(siteConfig);
      }
    });

    // 转换为key-value格式
    const configMap = configs.reduce((acc, config) => {
      acc[config.key] = {
        value: config.value,
        type: config.type,
        category: config.category,
        description: config.description,
        isPublic: config.isPublic,
        updatedAt: config.updatedAt
      };
      return acc;
    }, {} as Record<string, {
      value: string | null;
      type: string | null;
      category: string | null;
      description: string | null;
      isPublic: boolean | null;
      updatedAt: Date | null;
    }>);

    return NextResponse.json({
      success: true,
      data: configMap
    });

  } catch (error) {
    console.error('Get site config error:', error);
    return NextResponse.json({
      success: false,
      error: '获取站点配置失败'
    }, { status: 500 });
  }
}

// 创建或更新站点配置
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { projectId, key, value, type, category, description, isPublic } = body;

    if (!projectId || !key) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const configItem = await executeQuery(projectId, async (businessDb) => {
      // 检查配置是否已存在
      const existing = await businessDb
        .select({ id: siteConfig.id })
        .from(siteConfig)
        .where(eq(siteConfig.key, key))
        .limit(1);

      if (existing.length > 0) {
        // 更新现有配置
        const [updated] = await businessDb
          .update(siteConfig)
          .set({
            value,
            type: type || 'string',
            category: category || 'general',
            description,
            isPublic: isPublic !== undefined ? isPublic : false,
            updatedAt: new Date(),
          })
          .where(eq(siteConfig.key, key))
          .returning();
        return updated;
      } else {
        // 创建新配置
        const [inserted] = await businessDb
          .insert(siteConfig)
          .values({
            key,
            value,
            type: type || 'string',
            category: category || 'general',
            description,
            isPublic: isPublic !== undefined ? isPublic : false,
          })
          .returning();
        return inserted;
      }
    });

    return NextResponse.json({
      success: true,
      data: configItem,
      message: '站点配置保存成功'
    });

  } catch (error) {
    console.error('Save site config error:', error);
    const errorMessage = error instanceof Error ? error.message : '保存站点配置失败';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
} 