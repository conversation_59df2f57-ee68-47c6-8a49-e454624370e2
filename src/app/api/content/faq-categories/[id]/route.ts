import { faqCategoryTable } from "@/lib/db";
import { executeQuery } from "@/lib/db-manager";
import { desc, eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get("projectId");
    const { id } = await params;

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const [category] = await executeQuery(projectId, async (businessDb) => {
      return await businessDb
        .select()
        .from(faqCategoryTable)
        .where(eq(faqCategoryTable.id, parseInt(id)))
        .orderBy(faqCategoryTable.sort, desc(faqCategoryTable.createdAt))
        .limit(1);
    });

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error("Get categories error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "获取FAQ分类列表失败",
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get("projectId");
    const { id } = await params;
    const body = await request.json();
    const { name, slug, description, sort } = body;

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const [category] = await executeQuery(projectId, async (businessDb) => {
      return await businessDb
        .update(faqCategoryTable)
        .set({
          name,
          slug,
          description,
          sort,
        })
        .where(eq(faqCategoryTable.id, parseInt(id)))
        .returning();
    });

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error("Get categories error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "获取FAQ分类列表失败",
      },
      { status: 500 }
    );
  }
}
