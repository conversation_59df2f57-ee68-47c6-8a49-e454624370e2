import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db-manager';
import { pageMeta } from '@/lib/db/schema/business';
import { eq, desc, like, and, or, sql } from 'drizzle-orm';

// 获取页面元数据列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const language = searchParams.get('language') || '';

    if (!projectId) {
      return NextResponse.json({
        success: false,
        error: '缺少项目ID参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const result = await executeQuery(projectId, async (businessDb) => {
      const offset = (page - 1) * limit;

      // 构建查询条件
      const conditions = [];
      
      if (search) {
        conditions.push(
          or(
            like(pageMeta.path, `%${search}%`),
            like(pageMeta.title, `%${search}%`)
          )
        );
      }
      
      if (language) {
        conditions.push(eq(pageMeta.language, language));
      }

      // 查询页面元数据列表
      const metaList = await businessDb
        .select()
        .from(pageMeta)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(desc(pageMeta.createdAt))
        .limit(limit)
        .offset(offset);

      // 查询总数
      const totalResult = await businessDb
        .select({ count: sql<number>`count(*)` })
        .from(pageMeta)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const total = totalResult[0]?.count || 0;

      return {
        metaList,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        }
      };
    });

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Get page meta error:', error);
    return NextResponse.json({
      success: false,
      error: '获取页面元数据列表失败'
    }, { status: 500 });
  }
}

// 创建新的页面元数据
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      projectId,
      path,
      title,
      description,
      keywords,
      language,
      isActive,
      jsonLd,
      openGraph,
      canonical,
      noIndex
    } = body;

    if (!projectId || !path) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const newPageMeta = await executeQuery(projectId, async (businessDb) => {
      // 检查路径是否已存在
      const existing = await businessDb
        .select({ id: pageMeta.id })
        .from(pageMeta)
        .where(eq(pageMeta.path, path))
        .limit(1);

      if (existing.length > 0) {
        throw new Error('页面路径已存在');
      }

      // 创建页面元数据
      const [insertedMeta] = await businessDb
        .insert(pageMeta)
        .values({
          path,
          title,
          description,
          keywords,
          language: language || 'zh-CN',
          isActive: isActive !== undefined ? isActive : true,
          jsonLd: jsonLd ? JSON.stringify(jsonLd) : null,
          openGraph: openGraph ? JSON.stringify(openGraph) : null,
          canonical,
          noIndex: noIndex !== undefined ? noIndex : false,
        })
        .returning();

      return insertedMeta;
    });

    return NextResponse.json({
      success: true,
      data: newPageMeta,
      message: '页面元数据创建成功'
    });

  } catch (error) {
    console.error('Create page meta error:', error);
    const errorMessage = error instanceof Error ? error.message : '创建页面元数据失败';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
} 