import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db-manager';
import { pageMeta } from '@/lib/db/schema/business';
import { eq } from 'drizzle-orm';


// 获取单个页面元数据
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const url = new URL(request.url);
    const metaId = parseInt(url.pathname.split('/').pop() || '0');

    if (!projectId || !metaId) {
      return NextResponse.json({
        success: false,
        error: '缺少必要的参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const pageMetaItem = await executeQuery(projectId, async (businessDb) => {
      const [result] = await businessDb
        .select()
        .from(pageMeta)
        .where(eq(pageMeta.id, metaId))
        .limit(1);

      return result;
    });

    if (!pageMetaItem) {
      return NextResponse.json({
        success: false,
        error: '页面元数据不存在'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: pageMetaItem
    });

  } catch (error) {
    console.error('Get page meta error:', error);
    return NextResponse.json({
      success: false,
      error: '获取页面元数据失败'
    }, { status: 500 });
  }
}

// 更新页面元数据
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const metaId = parseInt(url.pathname.split('/').pop() || '0');
    const body = await request.json();
    const {
      projectId,
      path,
      title,
      description,
      keywords,
      language,
      isActive,
      jsonLd,
      openGraph,
      canonical,
      noIndex
    } = body;

    if (!projectId || !metaId || !path) {
      return NextResponse.json({
        success: false,
        error: '缺少必要的参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const updatedPageMeta = await executeQuery(projectId, async (businessDb) => {
      // 检查页面元数据是否存在
      const existing = await businessDb
        .select({ id: pageMeta.id })
        .from(pageMeta)
        .where(eq(pageMeta.id, metaId))
        .limit(1);

      if (existing.length === 0) {
        throw new Error('页面元数据不存在');
      }

      // 检查路径是否与其他记录冲突
      const pathConflict = await businessDb
        .select({ id: pageMeta.id })
        .from(pageMeta)
        .where(eq(pageMeta.path, path))
        .limit(1);

      if (pathConflict.length > 0 && pathConflict[0].id !== metaId) {
        throw new Error('页面路径已存在');
      }

      // 更新页面元数据
      const [updated] = await businessDb
        .update(pageMeta)
        .set({
          path,
          title,
          description,
          keywords,
          language: language || 'zh-CN',
          isActive: isActive !== undefined ? isActive : true,
          jsonLd: jsonLd ? JSON.stringify(jsonLd) : null,
          openGraph: openGraph ? JSON.stringify(openGraph) : null,
          canonical,
          noIndex: noIndex !== undefined ? noIndex : false,
          updatedAt: new Date(),
        })
        .where(eq(pageMeta.id, metaId))
        .returning();

      return updated;
    });

    return NextResponse.json({
      success: true,
      data: updatedPageMeta,
      message: '页面元数据更新成功'
    });

  } catch (error) {
    console.error('Update page meta error:', error);
    const errorMessage = error instanceof Error ? error.message : '更新页面元数据失败';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}

// 删除页面元数据
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const url = new URL(request.url);
    const metaId = parseInt(url.pathname.split('/').pop() || '0');

    if (!projectId || !metaId) {
      return NextResponse.json({
        success: false,
        error: '缺少必要的参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const deletedPageMeta = await executeQuery(projectId, async (businessDb) => {
      // 检查页面元数据是否存在
      const existing = await businessDb
        .select({
          id: pageMeta.id,
          path: pageMeta.path,
          title: pageMeta.title
        })
        .from(pageMeta)
        .where(eq(pageMeta.id, metaId))
        .limit(1);

      if (existing.length === 0) {
        throw new Error('页面元数据不存在');
      }

      // 删除页面元数据
      await businessDb
        .delete(pageMeta)
        .where(eq(pageMeta.id, metaId));

      return existing[0];
    });

    return NextResponse.json({
      success: true,
      data: {
        id: metaId,
        path: deletedPageMeta.path,
        title: deletedPageMeta.title
      },
      message: '页面元数据删除成功'
    });

  } catch (error) {
    console.error('Delete page meta error:', error);
    const errorMessage = error instanceof Error ? error.message : '删除页面元数据失败';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
} 