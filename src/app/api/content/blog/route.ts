import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db-manager';
import { blogPosts, blogCategories, blogTags, blogPostTags } from '@/lib/db/schema/business';
import { eq, desc, like, and, sql } from 'drizzle-orm';

// 获取博客文章列表
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('projectId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const status = searchParams.get('status') || '';

    if (!projectId) {
      return NextResponse.json(
        { error: '缺少项目ID参数' },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const result = await executeQuery(projectId, async (businessDb) => {
      const offset = (page - 1) * limit;

      // 构建查询条件
      const conditions = [];
      
      if (search) {
        conditions.push(like(blogPosts.title, `%${search}%`));
      }
      
      if (category && category !== '全部') {
        conditions.push(eq(blogPosts.categoryId, parseInt(category)));
      }
      
      if (status && status !== '全部') {
        conditions.push(eq(blogPosts.status, status));
      }

      // 查询文章列表
      const posts = await businessDb
        .select({
          id: blogPosts.id,
          title: blogPosts.title,
          slug: blogPosts.slug,
          summary: blogPosts.summary,
          thumbnail: blogPosts.thumbnail,
          status: blogPosts.status,
          isTop: blogPosts.isTop,
          viewCount: blogPosts.viewCount,
          likeCount: blogPosts.likeCount,
          publishedAt: blogPosts.publishedAt,
          createdAt: blogPosts.createdAt,
          updatedAt: blogPosts.updatedAt,
          authorName: blogPosts.authorName,
          authorEmail: blogPosts.authorEmail,
          categoryName: blogCategories.name,
        })
        .from(blogPosts)
        .leftJoin(blogCategories, eq(blogPosts.categoryId, blogCategories.id))
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(desc(blogPosts.createdAt))
        .limit(limit)
        .offset(offset);

      // 查询总数
      const totalResult = await businessDb
        .select({ count: sql<number>`count(*)` })
        .from(blogPosts)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const total = totalResult[0]?.count || 0;

      return {
        posts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        }
      };
    });

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Get blog posts error:', error);
    return NextResponse.json(
      { error: '获取文章列表失败' },
      { status: 500 }
    );
  }
}

// 创建新博客文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      projectId,
      title,
      slug,
      summary,
      content,
      categoryId,
      tags,
      status,
      thumbnail,
      metaTitle,
      metaDescription,
      metaKeywords,
      authorName,
      authorEmail,
    } = body;

    if (!projectId) {
      return NextResponse.json(
        { error: '缺少项目ID参数' },
        { status: 400 }
      );
    }

    // 验证必填字段
    if (!title || !content) {
      return NextResponse.json(
        { error: '标题和内容为必填项' },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const newPost = await executeQuery(projectId, async (businessDb) => {
      // 检查slug是否已存在
      if (slug) {
        const existing = await businessDb
          .select({ id: blogPosts.id })
          .from(blogPosts)
          .where(eq(blogPosts.slug, slug))
          .limit(1);

        if (existing.length > 0) {
          throw new Error('URL别名已存在');
        }
      }

      // 插入文章
      const [insertedPost] = await businessDb
        .insert(blogPosts)
        .values({
          title,
          slug: slug || title.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-'),
          summary,
          content,
          categoryId: categoryId ? parseInt(categoryId) : null,
          status: status || 'draft',
          thumbnail,
          metaTitle,
          metaDescription,
          metaKeywords,
          authorName,
          authorEmail,
          publishedAt: status === 'published' ? new Date() : null,
        })
        .returning();

      // 处理标签
      if (tags && Array.isArray(tags) && tags.length > 0) {
        for (const tagName of tags) {
          if (!tagName.trim()) continue;

          // 查找或创建标签
          let [tag] = await businessDb
            .select()
            .from(blogTags)
            .where(eq(blogTags.name, tagName.trim()))
            .limit(1);

                     if (!tag) {
             [tag] = await businessDb
               .insert(blogTags)
               .values({
                 name: tagName.trim(),
               })
               .returning();
           }

          // 关联文章和标签
          await businessDb
            .insert(blogPostTags)
            .values({
              postId: insertedPost.id,
              tagId: tag.id,
            })
            .onConflictDoNothing();
        }
      }

      return insertedPost;
    });

    return NextResponse.json({
      success: true,
      data: newPost,
      message: '文章创建成功'
    });

  } catch (error) {
    console.error('Create blog post error:', error);
    const errorMessage = error instanceof Error ? error.message : '创建文章失败';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
} 