import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db-manager';


// GET: 获取单个博客文章
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const url = new URL(request.url);
    const postId = parseInt(url.pathname.split('/').pop() || '0');

    if (!projectId || !postId) {
      return NextResponse.json({
        success: false,
        error: '缺少必要的参数'
      }, { status: 400 });
    }

    // 使用db-manager执行查询
    const result = await executeQuery(projectId, async (db, queryClient) => {
      return await queryClient`
        SELECT 
          p.*,
          c.name as category_name,
          c.slug as category_slug
        FROM blog_posts p
        LEFT JOIN blog_categories c ON p.category_id = c.id
        WHERE p.id = ${postId}
        LIMIT 1
      `;
    });

    if (result.length === 0) {
      return NextResponse.json({
        success: false,
        error: '文章不存在'
      }, { status: 404 });
    }

    const post = result[0];

    return NextResponse.json({
      success: true,
      data: {
        id: post.id,
        title: post.title,
        slug: post.slug,
        summary: post.summary,
        content: post.content,
        thumbnail: post.thumbnail,
        status: post.status,
        isTop: post.is_top,
        viewCount: post.view_count,
        likeCount: post.like_count,
        categoryId: post.category_id?.toString() || '',
        categoryName: post.category_name,
        tags: post.tags || [],
        metaTitle: post.meta_title || '',
        metaDescription: post.meta_description || '',
        metaKeywords: post.meta_keywords || '',
        authorName: post.author_name || '',
        authorEmail: post.author_email || '',
        publishedAt: post.published_at,
        createdAt: post.created_at,
        updatedAt: post.updated_at,
      }
    });

  } catch (error) {
    console.error('Get blog post error:', error);
    return NextResponse.json({
      success: false,
      error: '获取文章失败'
    }, { status: 500 });
  }
}

// PUT: 更新博客文章
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const postId = parseInt(url.pathname.split('/').pop() || '0');
    const body = await request.json();
    const {
      projectId,
      title,
      slug,
      summary,
      content,
      categoryId,
      tags,
      status,
      metaTitle,
      metaDescription,
      metaKeywords,
      authorName,
      authorEmail,
      thumbnail,
      isTop = false
    } = body;

    if (!projectId || !postId || !title || !content) {
      return NextResponse.json({
        success: false,
        error: '缺少必要的字段'
      }, { status: 400 });
    }

    // 使用db-manager执行查询
    const updatedPost = await executeQuery(projectId, async (db, queryClient) => {
      // 检查slug是否已存在（排除当前文章）
      if (slug) {
        const existingPost = await queryClient`
          SELECT id FROM blog_posts 
          WHERE slug = ${slug} AND id != ${postId}
          LIMIT 1
        `;

        if (existingPost.length > 0) {
          throw new Error('文章别名已存在');
        }
      }

      // 更新文章
      const result = await queryClient`
        UPDATE blog_posts SET
          title = ${title},
          slug = ${slug || null},
          summary = ${summary || null},
          content = ${content},
          category_id = ${categoryId ? parseInt(categoryId) : null},
          tags = ${JSON.stringify(tags || [])},
          status = ${status || 'draft'},
          meta_title = ${metaTitle || null},
          meta_description = ${metaDescription || null},
          meta_keywords = ${metaKeywords || null},
          author_name = ${authorName || null},
          author_email = ${authorEmail || null},
          thumbnail = ${thumbnail || null},
          is_top = ${isTop},
          published_at = ${status === 'published' ? 'NOW()' : null},
          updated_at = NOW()
        WHERE id = ${postId}
        RETURNING *
      `;

      return result[0];
    });

    return NextResponse.json({
      success: true,
      data: {
        id: updatedPost.id,
        title: updatedPost.title,
        slug: updatedPost.slug,
        summary: updatedPost.summary,
        content: updatedPost.content,
        thumbnail: updatedPost.thumbnail,
        status: updatedPost.status,
        isTop: updatedPost.is_top,
        viewCount: updatedPost.view_count,
        likeCount: updatedPost.like_count,
        categoryId: updatedPost.category_id?.toString() || '',
        tags: updatedPost.tags || [],
        metaTitle: updatedPost.meta_title || '',
        metaDescription: updatedPost.meta_description || '',
        metaKeywords: updatedPost.meta_keywords || '',
        authorName: updatedPost.author_name || '',
        authorEmail: updatedPost.author_email || '',
        publishedAt: updatedPost.published_at,
        createdAt: updatedPost.created_at,
        updatedAt: updatedPost.updated_at,
      }
    });

  } catch (error) {
    console.error('Update blog post error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '更新文章失败'
    }, { status: 500 });
  }
}

// DELETE: 删除博客文章
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const url = new URL(request.url);
    const postId = parseInt(url.pathname.split('/').pop() || '0');

    if (!projectId || !postId) {
      return NextResponse.json({
        success: false,
        error: '缺少必要的参数'
      }, { status: 400 });
    }

    // 使用db-manager执行查询
    const deletedPost = await executeQuery(projectId, async (db, queryClient) => {
      // 先获取文章信息用于返回
      const postResult = await queryClient`
        SELECT title FROM blog_posts WHERE id = ${postId} LIMIT 1
      `;

      if (postResult.length === 0) {
        throw new Error('文章不存在');
      }

      // 删除文章
      await queryClient`
        DELETE FROM blog_posts WHERE id = ${postId}
      `;

      return postResult[0];
    });

    return NextResponse.json({
      success: true,
      data: {
        id: postId,
        title: deletedPost.title
      }
    });

  } catch (error) {
    console.error('Delete blog post error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除文章失败'
    }, { status: 500 });
  }
} 