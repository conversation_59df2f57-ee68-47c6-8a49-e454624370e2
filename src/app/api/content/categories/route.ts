import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db-manager';
import { blogCategories } from '@/lib/db/schema/business';
import { eq, desc, like } from 'drizzle-orm';

// 获取博客分类列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const search = searchParams.get('search') || '';

    if (!projectId) {
      return NextResponse.json({
        success: false,
        error: '缺少项目ID参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const categories = await executeQuery(projectId, async (businessDb) => {
      if (search) {
        return await businessDb
          .select()
          .from(blogCategories)
          .where(like(blogCategories.name, `%${search}%`))
          .orderBy(blogCategories.sort, desc(blogCategories.createdAt));
      } else {
        return await businessDb
          .select()
          .from(blogCategories)
          .orderBy(blogCategories.sort, desc(blogCategories.createdAt));
      }
    });

    return NextResponse.json({
      success: true,
      data: categories
    });

  } catch (error) {
    console.error('Get categories error:', error);
    return NextResponse.json({
      success: false,
      error: '获取分类列表失败'
    }, { status: 500 });
  }
}

// 创建新分类
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { projectId, name, slug, description, sort } = body;

    if (!projectId || !name) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const newCategory = await executeQuery(projectId, async (businessDb) => {
      // 检查分类名是否已存在
      const existing = await businessDb
        .select({ id: blogCategories.id })
        .from(blogCategories)
        .where(eq(blogCategories.name, name))
        .limit(1);

      if (existing.length > 0) {
        throw new Error('分类名称已存在');
      }

      // 检查slug是否已存在
      if (slug) {
        const existingSlug = await businessDb
          .select({ id: blogCategories.id })
          .from(blogCategories)
          .where(eq(blogCategories.slug, slug))
          .limit(1);

        if (existingSlug.length > 0) {
          throw new Error('分类别名已存在');
        }
      }

      // 创建分类
      const [insertedCategory] = await businessDb
        .insert(blogCategories)
        .values({
          name,
          slug: slug || name.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-'),
          description,
          sort: sort || 0,
        })
        .returning();

      return insertedCategory;
    });

    return NextResponse.json({
      success: true,
      data: newCategory,
      message: '分类创建成功'
    });

  } catch (error) {
    console.error('Create category error:', error);
    const errorMessage = error instanceof Error ? error.message : '创建分类失败';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
} 