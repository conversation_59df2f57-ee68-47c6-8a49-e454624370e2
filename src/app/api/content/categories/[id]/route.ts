import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db-manager';
import { blogCategories } from '@/lib/db/schema/business';
import { eq } from 'drizzle-orm';

// 更新分类
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const categoryId = parseInt(url.pathname.split('/').pop() || '0');
    const body = await request.json();
    const { projectId, name, slug, description, sort } = body;

    if (!projectId || !name) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const updatedCategory = await executeQuery(projectId, async (businessDb) => {
      // 检查分类是否存在
      const existing = await businessDb
        .select({ id: blogCategories.id })
        .from(blogCategories)
        .where(eq(blogCategories.id, categoryId))
        .limit(1);

      if (existing.length === 0) {
        throw new Error('分类不存在');
      }

      // 检查名称是否与其他分类冲突
      const nameConflict = await businessDb
        .select({ id: blogCategories.id })
        .from(blogCategories)
        .where(eq(blogCategories.name, name))
        .limit(1);

      if (nameConflict.length > 0 && nameConflict[0].id !== categoryId) {
        throw new Error('分类名称已存在');
      }

      // 检查slug是否与其他分类冲突
      if (slug) {
        const slugConflict = await businessDb
          .select({ id: blogCategories.id })
          .from(blogCategories)
          .where(eq(blogCategories.slug, slug))
          .limit(1);

        if (slugConflict.length > 0 && slugConflict[0].id !== categoryId) {
          throw new Error('分类别名已存在');
        }
      }

      // 更新分类
      const [updated] = await businessDb
        .update(blogCategories)
        .set({
          name,
          slug: slug || name.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-'),
          description,
          sort: sort || 0,
          updatedAt: new Date(),
        })
        .where(eq(blogCategories.id, categoryId))
        .returning();

      return updated;
    });

    return NextResponse.json({
      success: true,
      data: updatedCategory,
      message: '分类更新成功'
    });

  } catch (error) {
    console.error('Update category error:', error);
    const errorMessage = error instanceof Error ? error.message : '更新分类失败';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}

// 删除分类
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const categoryId = parseInt(url.pathname.split('/').pop() || '0');
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (!projectId) {
      return NextResponse.json({
        success: false,
        error: '缺少项目ID参数'
      }, { status: 400 });
    }

    // 使用executeQuery统一处理数据库连接和查询
    const deletedCategory = await executeQuery(projectId, async (businessDb) => {
      // 检查分类是否存在
      const existing = await businessDb
        .select({
          id: blogCategories.id,
          name: blogCategories.name
        })
        .from(blogCategories)
        .where(eq(blogCategories.id, categoryId))
        .limit(1);

      if (existing.length === 0) {
        throw new Error('分类不存在');
      }

      // 删除分类
      await businessDb
        .delete(blogCategories)
        .where(eq(blogCategories.id, categoryId));

      return existing[0];
    });

    return NextResponse.json({
      success: true,
      data: {
        id: categoryId,
        name: deletedCategory.name
      },
      message: '分类删除成功'
    });

  } catch (error) {
    console.error('Delete category error:', error);
    const errorMessage = error instanceof Error ? error.message : '删除分类失败';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
} 