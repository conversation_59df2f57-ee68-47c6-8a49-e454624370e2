import { faqTable } from "@/lib/db";
import { executeQuery } from "@/lib/db-manager";
import { count, desc, asc, ilike, eq, and } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get("projectId");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";
    const sortBy = searchParams.get("sortBy") || "sort"; // sort, createdAt, question
    const sortOrder = searchParams.get("sortOrder") || "asc"; // asc, desc
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    // 使用executeQuery统一处理数据库连接和查询
    const { faqs, total } = await executeQuery(
      projectId,
      async (businessDb) => {
        const trimedSearch = search ? search.trim() : "";

        // Build conditions array
        const conditions = [];

        // Search condition
        if (trimedSearch) {
          conditions.push(ilike(faqTable.question, `%${trimedSearch}%`));
        }

        // Status filter
        if (status) {
          conditions.push(eq(faqTable.status, status));
        }

        // Combine conditions
        const whereCondition = conditions.length > 0 ? and(...conditions) : undefined;

        // Determine sort order
        let orderByClause;
        switch (sortBy) {
          case "question":
            orderByClause = sortOrder === "desc" ? desc(faqTable.question) : asc(faqTable.question);
            break;
          case "createdAt":
            orderByClause = sortOrder === "desc" ? desc(faqTable.createdAt) : asc(faqTable.createdAt);
            break;
          case "sort":
          default:
            orderByClause = sortOrder === "desc" ? desc(faqTable.sort) : asc(faqTable.sort);
            break;
        }

        const offset = (page - 1) * limit;
        const [total] = await businessDb
          .select({ count: count() })
          .from(faqTable)
          .where(whereCondition);

        const faqs = await businessDb
          .select()
          .from(faqTable)
          .where(whereCondition)
          .limit(limit)
          .offset(offset)
          .orderBy(orderByClause);
        return {
          faqs,
          total: total.count,
        };
      }
    );

    return NextResponse.json({
      success: true,
      data: faqs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Get faqs error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "获取FAQ列表失败",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { projectId, question, answer, categoryId, status, sort } = body;

    if (!projectId) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少项目ID参数",
        },
        { status: 400 }
      );
    }

    if (!question || !answer) {
      return NextResponse.json(
        {
          success: false,
          error: "问题和答案不能为空",
        },
        { status: 400 }
      );
    }

    const [faq] = await executeQuery(projectId, async (businessDb) => {
      return await businessDb
        .insert(faqTable)
        .values({
          question,
          answer,
          categoryId,
          status: status || "draft",
          sort: sort || 0,
        })
        .returning();
    });
    
    return NextResponse.json({
      success: true,
      data: faq,
    });
  } catch (error) {
    console.error("Create FAQ error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "创建FAQ失败",
      },
      { status: 500 }
    );
  }
}
