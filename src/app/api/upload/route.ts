import { NextRequest, NextResponse } from 'next/server';
import { uploadToS3 } from '@/lib/s3';
import { mainDb, businessProjects } from '@/lib/db';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const folder = formData.get('folder') as string || 'uploads';
    const alt = formData.get('alt') as string || '';

    if (!file) {
      return NextResponse.json(
        { error: '没有选择文件' },
        { status: 400 }
      );
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: '不支持的文件类型' },
        { status: 400 }
      );
    }

    // 验证文件大小 (最大5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: '文件大小不能超过5MB' },
        { status: 400 }
      );
    }

    // 处理projectId参数
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    let bucket: string = "pumpsoul-new";
    let cdn: string = "https://res.pumpsoul.com";
    if (projectId && parseInt(projectId) > 0) {
      // 查询项目配置
      const projects = await mainDb
        .select({ config: businessProjects.config })
        .from(businessProjects)
        .where(eq(businessProjects.id, parseInt(projectId)))
        .limit(1);
      if (projects.length > 0 && projects[0].config?.s3) {
        bucket = projects[0].config.s3.bucket;
        cdn = projects[0].config.s3.cdn;
      }
    }

    // 上传到S3
    const result = await uploadToS3(file, {
      folder,
      contentType: file.type,
      bucket,
    });

    // 返回cdn url（如有）
    let url = result.url;
    if (cdn && result.key) {
      url = `${cdn.replace(/\/$/, '')}/${result.key}`;
    }

    return NextResponse.json({
      success: true,
      url,
      key: result.key,
      alt,
      size: file.size,
      type: file.type,
      name: file.name,
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: '上传失败，请重试' },
      { status: 500 }
    );
  }
} 