import { NextRequest, NextResponse } from 'next/server';
import { mainDb, systemConfig } from '@/lib/db';
import { eq } from 'drizzle-orm';

// 获取系统配置
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    if (!key) {
      return NextResponse.json({
        success: false,
        error: '缺少配置键参数'
      }, { status: 400 });
    }

    // 查询系统配置
    const [config] = await mainDb
      .select()
      .from(systemConfig)
      .where(eq(systemConfig.key, key))
      .limit(1);

    if (!config) {
      return NextResponse.json({
        success: false,
        error: '配置不存在'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: {
        key: config.key,
        value: config.value,
        description: config.description,
        updatedAt: config.updatedAt
      }
    });

  } catch (error) {
    console.error('Get system config error:', error);
    return NextResponse.json({
      success: false,
      error: '获取系统配置失败'
    }, { status: 500 });
  }
}

// 创建或更新系统配置
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { key, value, description } = body;

    if (!key) {
      return NextResponse.json({
        success: false,
        error: '缺少配置键参数'
      }, { status: 400 });
    }

    // 检查配置是否已存在
    const existing = await mainDb
      .select({ id: systemConfig.id })
      .from(systemConfig)
      .where(eq(systemConfig.key, key))
      .limit(1);

    let configItem;
    if (existing.length > 0) {
      // 更新现有配置
      const [updated] = await mainDb
        .update(systemConfig)
        .set({
          value,
          description,
          updatedAt: new Date(),
        })
        .where(eq(systemConfig.key, key))
        .returning();
      configItem = updated;
    } else {
      // 创建新配置
      const [inserted] = await mainDb
        .insert(systemConfig)
        .values({
          key,
          value,
          description,
        })
        .returning();
      configItem = inserted;
    }

    return NextResponse.json({
      success: true,
      data: configItem,
      message: '系统配置保存成功'
    });

  } catch (error) {
    console.error('Save system config error:', error);
    const errorMessage = error instanceof Error ? error.message : '保存系统配置失败';
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
} 