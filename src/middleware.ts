import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";

export default auth((req) => {
  const { nextUrl } = req;

  // 定义需要保护的路由
  const isAdminRoute = nextUrl.pathname.startsWith('/admin');
  const isApiAdminRoute = nextUrl.pathname.startsWith('/api/admin');
  const isAuthPage = nextUrl.pathname.startsWith('/auth');

  // 通过 NextAuth 检查用户是否已登录
  const isLoggedIn = !!req.auth;

  // 如果访问管理员路由但未登录，重定向到登录页面
  if ((isAdminRoute || isApiAdminRoute) && !isLoggedIn) {
    return NextResponse.redirect(new URL('/auth/signin', nextUrl));
  }

  // 如果已登录但访问登录页面，重定向到管理页面
  if (isAuthPage && isLoggedIn) {
    return NextResponse.redirect(new URL('/admin', nextUrl));
  }

  // 其他情况继续正常访问
  return NextResponse.next();
});

export const config = {
  matcher: [
    // 保护所有管理员路由
    '/admin/:path*',
    // 保护管理员API路由
    '/api/admin/:path*',
    // 处理认证页面
    '/auth/:path*',
  ],
}; 